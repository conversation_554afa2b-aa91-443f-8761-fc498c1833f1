在当前目录作为项目根目录，写一个Visual Studio 2022项目的视窗exe软件用于使用Aspose.Slides功能对ppt批量处理，aspose版本为25.4.0.0，功能实现参考Aspose.Slides官方api文档，aspose使用项目目录里的版本，许可证在项目里Aspose.Total.NET.lic  ，项目根目录里的Aspose.Slides.xml是官方api使用文档。.net版本要求为.net  6.0  。软件要时标准软件目录架构，注意软件风格美化，要现代化风格配色，所有功能配置文件放在config目录中的对应配置文件中，便于用户同一修改软件默认配置。
相关功能要求如下
软件布局：软件由上而下分为若干区域，程序要能支持高dpi环境
一、路径区域：
1、来源目录（可选是否处理子目录）及浏览按钮。支持拖放文件夹获取来源目录路径。
2、目标区域（处理后的文件到达复制到的位置，可选是否保持原目录结构）及浏览按钮。支持拖放文件夹获取目标目录路径。
3、移动文件时冲突处理选项。是否处理源文件勾选项、复制移动文件单选项。
相关逻辑如下：如果勾选了处理源文件，那么直接对源文件进行各项功能处理，格式转换生成的文件路径与源文件一致。如果没勾选处理源文件，选择的是复制模式，那么就将源文件复制到输出目录后，再进行对应功能处理，格式转换生成的文件路径为输出目录。如果没勾选处理源文件，选择的是移动模式，那么就将源文件移动到输出目录后删除源文件，再进行对应功能处理，格式转换生成的文件路径为输出目录。复制移动遇到文件冲突遵循用户选择进行对应处理。
4、线程数设定输入框。程序根据用户设定的线程数进行多线程处理。
5、重试次数设定输入框，当某个文件处理失败时，自动尝试重试，达到重试次数还是失败，则跳过该文件后续处理流程，该文件被记为处理失败。
单个在处理过程中任何一个步骤失败，就按照用户设定的重试次数进行重试处理，达到重试次数后还处理失败就标记为处理失败，然后进行后续步骤的处理，直至所有步骤都尝试处理完。
6、支持格式（弹窗设置，勾选的文档格式判断对应的文件才加入处理流程）。
7、批处理数量输入框。
8、全选、取消全选按钮（用于对下面的功能区域勾选框进行批量勾选、批量取消勾选）

二、功能区域，九个功能按钮分为3*3排列，每个功能前有个单独启用框。每个功能弹窗设置详细内容，弹窗内每类子功能单独一个区域，如果子功能分类多就分多个标签页，每个子功能有单独的启用开关，每个标签页要有总开关。每个子功能你根据Aspose.Slides支持的功能自行细化拓展。
（一） 页面设置（子功能按种类分为多个标签页）
1、幻灯片尺寸设置
2、幻灯片方向
横向/纵向切换
尺寸比例调整

（二）内容删除设置（子功能按种类分为多个标签页）
1、删除文档
（1）判断文件名长度是否在XX字符至XX字符之间（如果勾选该子功能启用开关，判断当前处理的的文档名称不含拓展名字符长度是否符合，如果不符合进行下一步，如果符合就删除当前文档）
（2）判断文档大小是否在XX KB至XX KB之间（如果勾选该子功能启用开关，判断当前处理的文档大小是否符合要求，如果不符合进行下一步处理，如果符合就删除当前文档。文档大小单位可选b、kb、mb，两个输入框要有自己单独的单位可选，处理时统一转换为b大小进行比较。）
（3）判断文档内容字符总数是否在XX 字符至 XX 字符之间（如果勾选该子功能开关，判断当前处理的文档内容字符总数是否符合要求，如果不符合进行过下一步处理，如果符合就删除当前文档）
（4）判断文档页数是否在 XX页 至XX页之间（如果勾选该子功能开关，判断当前处理文档的页数是否符合要求，如果不符合进行过下一步处理，如果符合就删除当前文档，默认填充1页至3页）
（5）判断文档名称是否包含非法词（如果勾选该子功能开关，判断当前处理文档的文件名是否含有某以个设定的非法词，如果文件名不含任意一个非法词进行过下一步处理，如果含有非法词就删除当前文档。文件名非法词在对应弹窗中设置，一行一个非法词，此非法词单独存一个配置文件因为非法词可能含特殊字符）
（6）判断文档内容是否包含非法词（如果勾选该子功能开关，判断当前处理文档的内容是否含有某个设定的非法词，如果不含任何一个非法词就进行过下一步处理，如果含有非法词就删除当前文档。内容非法词在对应弹窗中设置，一行一个非法词，此非法词单独存一个配置文件因为非法词可能含特殊字符）
如果文档在当前步骤被删除了，那就自动跳过该文档后续处理流程，继续处理下一个文档。
2、内容删除设置（可选删除范围）
删除空白幻灯片
删除指定页幻灯片（删除第一页、删除最后一页、第X页-第X页、删除包含指定关键词的页）
删除空白段落
删除空白行
3、文本删除（删除包含指定文本的段落、删除包含指定文本的文本框、删除包含指定文本的表格）
4、删除图片【删除所有图片、删除指定图片（可指定多个图片）、删除第X页-第X页幻灯片中的所有图片、删除ppt最后一页图片、删除ppt中定位完全在用户设定矩形区域内的图片】、删除背景图片
5、删除表格（删除PPT第X页-第X页里的表格、删除所有表格、删除最后一页里的表格）
6、删除图表（删除PPT第X页-第X页里的图表、删除所有图表、删除最后一页里的图表）
7、删除音频视频（删除PPT第X页-第X页里的音频、删除所有音频、、删除最后一页里的音频、删除PPT第X页-第X页里的视频、删除所有视频、删除最后一页里的视频）
8、删除联系方式（删除手机号码、删除固定电话号码、删除电子邮箱、删除网址、删除超链接）
9、动画删除
删除所有动画效果
删除指定动画（删除PPT第X页-第X页里的动画效果、删除所有动画效果、删除最后一页里的动画效果）
删除切换效果（删除PPT第X页-第X页里的切换效果、删除所有切换效果、删除最后一页里的切换效果）
10、备注删除
删除幻灯片备注
清空备注内容
11、删除全文格式：（删除字体格式、删除段落格式、删除表格格式、删除列表格式）

（三）内容替换设置（子功能按种类分为多个标签页）
1、文本替换（能添加多条替换规则到规则列表，每条规则要有启用开关）
普通文本替换
正则表达式替换
批量文本替换
指定范围替换（标题、内容、备注）
2、形状替换
图片替换
文本框替换
形状样式替换
3、字体替换
字体名称替换
字体样式替换
字体嵌入
4、颜色替换
主题颜色替换
自定义颜色替换

（四）PPT全局格式设置（子功能按种类分为多个标签页）
1、段落格式设置
（1）对齐方式：左对齐、居中、右对齐、两端对齐、分散对齐
（2）缩进：文本之前缩进距离，特殊缩进（无、首行缩进、悬挂缩进XX字符）
（3）间距：段前XX磅，段后XX磅，行距（单倍行距、1.5倍行距、2倍行距、多倍行距、固定值  XX）
（4）勾选项：按中文习惯控制中文首尾字符、允许西文在单词中间换行、允许标点移除边界。
（5）文本对齐方式：自动、居中、基线、底部
2、字体格式设置
中文字体、西文字体、字体样式（常规、倾斜、加粗、倾斜加粗）、字体大小、字体颜色、下划线线型、下划线颜色
文字效果：删除线、双删除线、上表、下标
3、主题设置
应用内置主题
自定义主题
主题颜色方案
主题字体方案
4、母版设置
幻灯片母版
标题母版
备注母版
讲义母版
5、布局设置
标题幻灯片布局
内容布局
两栏布局
图片布局
自定义布局
6、样式设置
形状样式
文本样式
表格样式
图表样式

（五）匹配段落格式（根据匹配条件对局部内容进行格式设置，可以设定多个匹配规则，每个规则要有启用开关能单独控制是否启用）
1、匹配条件（匹配段落开头、段落包含关键词、匹配段落结尾、使用正则表达式）、段落字符数限制XX字符-XX字符
2、段落格式设置
（1）对齐方式：左对齐、居中、右对齐、两端对齐、分散对齐
（2）缩进：文本之前缩进距离，特殊缩进（无、首行缩进、悬挂缩进XX字符）
（3）间距：段前XX磅，段后XX磅，行距（单倍行距、1.5倍行距、2倍行距、多倍行距、固定值  XX）
（4）勾选项：按中文习惯控制中文首尾字符、允许西文在单词中间换行、允许标点移除边界。
（5）文本对齐方式：自动、居中、基线、底部
3、字体格式设置
中文字体、西文字体、字体样式（常规、倾斜、加粗、倾斜加粗）、字体大小、字体颜色、下划线线型、下划线颜色
文字效果：删除线、双删除线、上表、下标

（六）PPT页脚设置
1、删除页眉、页脚功能（可指定删除范围）
2. 基础PPT页脚设置
演示文稿级别的PPT页脚设置
单个幻灯片的PPT页脚设置
母版幻灯片PPT页脚设置
布局幻灯片PPT页脚设置
3. 普通幻灯片PPT页脚设置
（1）页脚文本设置
设置所有幻灯片页脚文本
设置所有幻灯片页脚可见性
（2）页眉文本设置
通过占位符形状设置页眉文本
支持页眉可见性控制
（3）页码设置
页码显示/隐藏控制
页码格式设置
（4）日期时间设置
日期时间显示/隐藏控制
日期时间格式设置
自动更新日期时间选项
4、备注幻灯片PPT页脚设置
备注母版PPT页脚设置
备注幻灯片PPT页脚设置


（七）文档属性（子功能按种类分为多个标签页）
1、删除文档属性功能
2、属性设置功能
（1）基本信息属性
标题
作者
主题
关键词
描述/注释
类别
公司
管理者
（2）统计属性
幻灯片数量
隐藏幻灯片数
备注页数
段落数量
字数统计
多媒体剪辑数
（3）时间属性
创建时间
最后保存时间
最后保存者
最后打印时间
总编辑时间



（八） 文件名替换（文件名替换，是指在对文档进行完处理后，对最终的文档进行文件名替换。）
1、文件名模式替换（能添加多条替换规则到规则列表，每条规则要有启用开关）
字符替换（支持正则替换）
要有导入导出功能。

（九）PPT格式转换（子功能按种类分为多个标签页）
1、转PDF
（1）pdf转换基本设置
PPT/PPTX转PDF
批量转换
保持原始格式
（2）、转换选项
页面范围选择
图片质量设置
压缩选项
密码保护
（3）、布局选项
幻灯片布局
讲义布局
备注布局
大纲布局
（4）、高级选项
嵌入字体
图片压缩
元数据保留
书签生成
2、转图片
PPT转PNG
PPT转JPEG
PPT转BMP
PPT转TIFF
PPT转SVG
3、转Web
PPT转HTML
PPT转XAML
4、转其他
PPT转XPS
PPT转SWF
幻灯片缩略图生成

三、功能按钮区域
1、开始处理、定时处理、停止处理、日志设置、清空日志、导出配置、导入配置、打开源目录、打开输出目录  九个按钮分三行、每行三列均匀分布。
2、定时处理分为一次性启动、指定时间启动、倒计时启动，三种模式单选互斥， 每种设置单独一个标签页设置
（1）一次性启动，用户指定一个具体时间  年月日时分秒，到了指定时间启动一次“开始处理”。
（2）指定时间启动：可以设置每年、每月、每天、每时的某个具体时间启动，时间到就启动一次“开始处理”。
如果选择了每年启动，则还可以选择启动的月、日、时、分、秒。
如果选择了每月启动，则还可以选择启动的日、时、分、秒。
如果选择了每天启动，则还可以选择启动的时、分、秒。
如果选择了每时启动，则还可以选择启动的分、秒。
（3）倒计时启动：用户设定一个具体的第一次启动开始时间  年月日时分秒  ，和间隔时间  XX天XX小时XX分XX秒。到了第一次启动时间后，开始倒计时，每次倒计时结束后启动一次“开始处理”。
（4）一个公共区域显示高级设置：此设置对指定时间启动和倒计时启动使用。高级设置用户限定循环次数和结束时机
①无限制
②运行次数限制：XX次。  用户手动输入循环的次数。
③过期时间限制：指定一个到期时间，到了该时间后就停止循环。
3、任务运行过程中开始处理按钮不可用，停止处理按钮可用。任务没运行时，停止处理按钮不可用。
4、点击日志设置弹窗显示日志勾选窗口，出现日志设置弹窗显示当前软件支持的所有日志类型，每个日志类型左侧有个勾选启用框，最上面有个总开关。用户可以根据自己的需求，对不同类型日志进行勾选，勾选的日志类型才对应的写入Log目录对应日志文件，没勾选的日志类型不写入日志文件。
5、点击清空日志按钮、删除所有日志文件。
6、导出配置按钮，一键将所有配置压缩打包导出。
7、导入配置按钮，导入备份的配置文件压缩包，覆盖当前配置文件。

四、数据统计区域
1、统计总文件数、成功处理数及百分数、处理失败数及百分数、重试次数及百分数、开始时间、预计结束时间    分两行显示
2、进度条
3、处理速度XX文件/分钟    处理耗时：XX：XX:XX   

五、其它要求
1、软件面板不显示实时处理日志，处理日志只保存在软件根目录Log目录里对应日志文件中，为减少IO占用，日志10秒钟写入一次日志文件。
2、用户所有配置分功能自动保存在生成软件根目录config目录中的对应配置文件中，一个大功能一个配置文件，以便于下次打开软件可以直接套用上一次的设置。注意配置文件的序列化与反序列化，注意软件初始化与配置文件加载逻辑的处理，避免软件初始化时的配置文件覆盖用户设定的已有的配置文件。
3、所有大小功能都为可选功能，根据是否勾选决定是否启用本功能。
4、代码编写过程中尽量避免空引用警告，避免出现编译错误。
5、所有输入框和下拉框中的文字要居中显示。
6、全程中文交流，每次修改代码前先查看所有项目文件每一行代码，并参考aspose官方文档，了解内容，整体思考，修改完代码后，检查所有文件代码看是否有错误，如果有错误，自动修复错误，确认没问题了自动编译运行。
7、代码添加详细中文注释
8、注意各组件位置布局，避免组件重叠的现象。合理设置行高，使得所有文字都完整显示。
9、各功能弹窗中别设置二次弹窗，要全员集成到对应标签页中，相应的座位单独的区域，每个功能要有单独的启用开关。
9、各标签页下的各区域，一横排只放一个区域，这样就有足够的宽度空间完整显示所有组件了。
10、全面优化文档属性设置窗体的布局。设置合理窗口大小、区域大小及高度、调整组件、按钮、标签的宽度、边距、增加行距和间距。字体大小（不低于10F），行高不低于40，精确计算各区域的位置，确保所有区域、标签文字、所有内容都能完整显示。避免区域重叠、组件重叠。每行内容要在同一水平线。我要布局其看起来相对宽松。还要各输入框和下拉框中的文字居中显示。
11、检查所有按钮的事件处理有没有正确绑定，所有逻辑是否已经完成。查看Aspose.Slides.xml分析内容删除设置里面的所有功能是否全部正常可用。


检查软件处理流程是否有问题：
1、软件初始化、注意软件初始化与配置文件的加载顺序，避免默认配置覆盖用户设定的配置文件。
如果软件生成目录里的Config目录下不存在对应的配置文件，则在软件生成目录创建默认的配置文件，如果Config目录下已有用户设定的配置文件，则使用已有的配置文件。
2、根据支持格式设定从源目录读取对应格式文件加入处理流程
3、根据用户设定的是否直接处理源文件、移动模式、冲突处理方法、线程数、批处理数量对文件进行处理，遇到冲突文件时遵循用户设定的冲突处理方式进行处理。
4、9个处理功能根据是否勾选加入处理流程，具体的顺序是：内容删除设置、内容替换设置、页面设置、PPT格式设置、匹配段落格式、PPT页脚设置、文档属性设置、文件名替换、PPT格式转换（注意上一步文件名替换导致的文件路径变更）。各大功能里面的子功能，根据勾选情况进行处理，没勾选的步骤不参与处理。
5、处理过程中，开始处理按钮禁用，停止处理按钮可用，日志系统记录对应步骤日志到Log目录下对应日志文件中。
6、处理过程中，处理统计区域实时显示处理数据和处理进度。
7、处理过程中，日志进行对应记录。日志分类放在多个日志文件中，日志文件名添加日期后缀。
8、检查下此软件还有没有什么其他问题，看有没有逻辑错误、处理流程错误、代码错误，需要aspose.Slides处理的是否符合项目根目录的Aspose.Slides.xml规范。Aspose.Slides许可证加载使用流程是否正常，目前使用的是否是我提供的项目根目录的Aspose.Slides.dll  其版本号为25.4.0.0

检查XX功能下的所有子功能是否符合项目根目录的Aspose.Slides.xml规范（Aspose.Slides的API规范），其对应的所有功能和子功能是否已经正常实现并可用，是否有逻辑问题或者代码错误，每个子功能勾选后是否能按顺序独立执行。能否根据用户勾选项进行对应功能处理。此功能的配置文件是否与该功能一致。各输入框和下拉框中的文字要居中显示，多行文本框文字左对齐，各表格里的表头和单元格内容要居中显示。删掉多余的确认提示框，因为频繁跳出的确认窗口没必要。功能开关要有对应中文注释说清用途。该功能的每个cs文件顶部要有注释说明当前文件用途。点击窗口的确定按钮或者直接关闭窗口，配置文件自动更新保存，点击取消按钮关闭窗口配置文件不更新保存。配置文件加载应用到UI中的逻辑、顺序是否正确。检查此功能相关文件中有没有冗余代码，清除冗余代码。


检查PPT全局格式设置中的“样式设置”标签页下的相关功能是否符合项目根目录的Aspose.Slides.xml规范（Aspose.Slides的API规范），其对应的所有功能和子功能是否已经正常实现并可用，是否有逻辑问题或者代码错误，每个子功能勾选后是否能按顺序独立执行。能否根据用户勾选项进行对应功能处理。此功能的配置文件是否与该功能一致。各输入框和下拉框中的文字要居中显示，多行文本框文字左对齐，各表格里的表头和单元格内容要居中显示。删掉多余的确认提示框，因为频繁跳出的确认窗口没必要。功能开关要有对应中文注释说清用途。该功能的每个cs文件顶部要有注释说明当前文件用途。点击窗口的确定按钮或者直接关闭窗口，配置文件自动更新保存，点击取消按钮关闭窗口配置文件不更新保存。配置文件加载应用到UI中的逻辑、顺序是否正确。

检查内容删除设置中的“删除图片”标签页下的相关子功能是否符合项目根目录的Aspose.Slides.xml规范（Aspose.Slides的API规范），其对应的所有功能和子功能是否已经正常实现并可用，是否有逻辑问题或者代码错误，每个子功能勾选后是否能按顺序独立执行。能否根据用户勾选项进行对应功能处理。此功能的配置文件是否与该功能一致。点击窗口的确定按钮或者直接关闭窗口，配置文件自动更新保存，点击取消按钮关闭窗口配置文件不更新保存。配置文件加载应用到UI中的逻辑、顺序是否正确。


参考项目根目录的Aspose.Slides.xml规范（Aspose.Slides的API规范），对于内容删除设置功能里，在内容删除设置、文本删除、删除图片、删除表格、删除图表、删除音频视频、删除联系方式、动画删除、备注删除、删除全文格式   每个标签页下最上方添加一个删除范围区域，里面可选删除范围   普通幻灯片页、母版、母版版式页 （默认全选） ，用于可以根据自己的需要进行勾选，程序在处理时根据用户对应勾选范围进行对应删除操作


你应该完善一下日志系统，详细记录 程序初始化信息、许可证加载、文件处理情况、错误情况应该详细记录


检查内容删除设置中的“删除图片”标签页下的已有子功能是否符合项目根目录的Aspose.Slides.xml规范（Aspose.Slides的API规范），其对应的所有功能和子功能是否已经正常实现并可用，是否有逻辑问题或者代码错误，每个子功能勾选后是否能按顺序独立执行。能否根据用户勾选项进行对应功能处理。此功能的配置文件是否与该功能一致。


给内容替换设置功能里的每个标签页添加一个“替换范围”区域、区域里有  普通幻灯片页、母版页、母版版式页  三个选项（默认全选）。此区域放在每个标签页下最上方，总开关下方。程序更具用户范围勾选，在对应的范围进行内容替换。


使用标准的 Windows Forms 设计器模式，而是在一个单独的文件中手动编写了所有的 InitializeComponent 代码。这种情况下，我们需要用不同的方法来设置图标。

删除  内容删除设置  窗口底部的重置、应用按钮，保留确定、取消按钮靠右放置。因为点击确定按钮时会自动保存配置后关闭窗口、点击关闭窗口操作时会自动保存配置，取消按钮是关闭窗口不保存配置，够用了。