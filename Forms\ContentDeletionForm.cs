// PPT内容删除设置窗体
// 功能：配置PPT内容删除的各种选项，包括文档删除、内容删除、文本删除、图片删除等11个分类
// 作者：PPT批量处理工具开发团队
// 创建时间：2024年
// 最后修改：2024年

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 内容删除设置窗体
    /// </summary>
    public partial class ContentDeletionForm : Form
    {
        // 当前设置
        private ContentDeletionSettings _currentSettings = null!;

        // 各标签页的控件容器
        private readonly Dictionary<string, Panel> _tabPanels = new Dictionary<string, Panel>();

        public ContentDeletionForm()
        {
            InitializeComponent();
            InitializeSettings();
            InitializeTabPages();
            SetupEventHandlers();

            // 启用按键预处理，确保多行文本框按键不被窗体拦截
            this.KeyPreview = true;
        }

        /// <summary>
        /// 初始化设置
        /// </summary>
        private void InitializeSettings()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                _currentSettings = config.ContentDeletionSettings;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载内容删除设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                _currentSettings = new ContentDeletionSettings();
            }
        }

        /// <summary>
        /// 初始化标签页
        /// </summary>
        private void InitializeTabPages()
        {
            try
            {
                // 初始化删除文档标签页
                InitializeDocumentDeletionTab();

                // 初始化内容删除设置标签页
                InitializeContentRemovalTab();

                // 初始化文本删除标签页
                InitializeTextDeletionTab();

                // 初始化图片删除标签页
                InitializeImageDeletionTab();

                // 初始化表格删除标签页
                InitializeTableDeletionTab();

                // 初始化图表删除标签页
                InitializeChartDeletionTab();

                // 初始化音频视频删除标签页
                InitializeMediaDeletionTab();

                // 初始化联系方式删除标签页
                InitializeContactDeletionTab();

                // 初始化动画删除标签页
                InitializeAnimationDeletionTab();

                // 初始化备注删除标签页
                InitializeNotesDeletionTab();

                // 初始化格式删除标签页
                InitializeFormatDeletionTab();

                // 加载当前设置到界面
                LoadSettingsToUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化标签页失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化删除文档标签页
        /// </summary>
        private void InitializeDocumentDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageDocumentDeletion.Controls.Add(panel);
            _tabPanels["DocumentDeletion"] = panel;

            int yPos = 20;

            // 总开关 - 使用自定义尺寸以确保完整显示较长的文字
            var chkMasterSwitch = CreateCheckBox("启用删除文档功能（删除符合条件的文档）", 20, yPos, 480, 40);
            chkMasterSwitch.Name = "chkDocumentDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70; // 增加间距

            // 删除范围选择区域
            var grpDeletionScope = CreateDeletionScopeGroupBox(20, yPos, 1200, "DocumentDeletion");
            panel.Controls.Add(grpDeletionScope);
            yPos += 120; // 增加间距以适应新的高度

            // 分组框：文件名长度检查
            var grpFileNameLength = CreateGroupBox("文件名长度检查", 20, yPos, 1200, 150);
            var chkFileNameLength = CreateCheckBox("启用文件名长度检查", 30, 50, 200, 40);
            chkFileNameLength.Name = "chkFileNameLengthCheck";
            var lblFileNameMin = CreateLabel("最小长度:", 280, 50, 100, 40);
            var numFileNameMin = CreateNumericUpDown(1, 1000, 390, 50, 100, 40);
            numFileNameMin.Name = "numFileNameMinLength";
            var lblFileNameMax = CreateLabel("最大长度:", 520, 50, 100, 40);
            var numFileNameMax = CreateNumericUpDown(1, 1000, 630, 50, 100, 40);
            numFileNameMax.Name = "numFileNameMaxLength";
            var lblFileNameUnit = CreateLabel("字符", 760, 50, 60, 40);

            grpFileNameLength.Controls.AddRange(new Control[] {
                chkFileNameLength, lblFileNameMin, numFileNameMin,
                lblFileNameMax, numFileNameMax, lblFileNameUnit
            });
            panel.Controls.Add(grpFileNameLength);
            yPos += 170;

            // 分组框：文件大小检查
            var grpFileSize = CreateGroupBox("文件大小检查", 20, yPos, 1200, 150);
            var chkFileSize = CreateCheckBox("启用文件大小检查", 30, 50, 200, 40);
            chkFileSize.Name = "chkFileSizeCheck";

            // 统一Y坐标，确保所有控件文字在同一水平线上
            const int controlY = 50; // 统一的Y坐标
            const int controlHeight = 40; // 统一的控件高度为40

            // 最小大小设置 - 使用自定义尺寸
            var lblFileSizeMin = CreateLabel("最小大小:", 280, controlY, 100, controlHeight);
            lblFileSizeMin.TextAlign = ContentAlignment.MiddleLeft; // 确保文字垂直居中

            var numFileSizeMin = CreateNumericUpDown(1, 999999, 390, controlY, 100, controlHeight);
            numFileSizeMin.Name = "numFileSizeMin";

            var cmbFileSizeMinUnit = CreateComboBox(new[] { "B", "KB", "MB", "GB" }, 500, controlY, 80, controlHeight);
            cmbFileSizeMinUnit.Name = "cmbFileSizeMinUnit";

            // 最大大小设置 - 使用自定义尺寸
            var lblFileSizeMax = CreateLabel("最大大小:", 620, controlY, 100, controlHeight);
            lblFileSizeMax.TextAlign = ContentAlignment.MiddleLeft; // 确保文字垂直居中

            var numFileSizeMax = CreateNumericUpDown(1, 999999, 730, controlY, 100, controlHeight);
            numFileSizeMax.Name = "numFileSizeMax";

            var cmbFileSizeMaxUnit = CreateComboBox(new[] { "B", "KB", "MB", "GB" }, 840, controlY, 80, controlHeight);
            cmbFileSizeMaxUnit.Name = "cmbFileSizeMaxUnit";

            grpFileSize.Controls.AddRange(new Control[] {
                chkFileSize, lblFileSizeMin, numFileSizeMin, cmbFileSizeMinUnit,
                lblFileSizeMax, numFileSizeMax, cmbFileSizeMaxUnit
            });
            panel.Controls.Add(grpFileSize);
            yPos += 170;

            // 分组框：内容字符数检查
            var grpContentCharCount = CreateGroupBox("内容字符数检查", 20, yPos, 1200, 150);
            var chkContentCharCount = CreateCheckBox("启用内容字符数检查", 30, 50, 220, 40);
            chkContentCharCount.Name = "chkContentCharCountCheck";
            var lblContentCharMin = CreateLabel("最小字符数:", 300, 50, 120, 40);
            var numContentCharMin = CreateNumericUpDown(1, 999999, 430, 50, 120, 40);
            numContentCharMin.Name = "numContentCharMin";
            var lblContentCharMax = CreateLabel("最大字符数:", 580, 50, 120, 40);
            var numContentCharMax = CreateNumericUpDown(1, 999999, 710, 50, 120, 40);
            numContentCharMax.Name = "numContentCharMax";

            grpContentCharCount.Controls.AddRange(new Control[] {
                chkContentCharCount, lblContentCharMin, numContentCharMin,
                lblContentCharMax, numContentCharMax
            });
            panel.Controls.Add(grpContentCharCount);
            yPos += 170;

            // 分组框：页数检查
            var grpPageCount = CreateGroupBox("页数检查", 20, yPos, 1200, 150);
            var chkPageCount = CreateCheckBox("启用页数检查", 30, 50, 180, 40);
            chkPageCount.Name = "chkPageCountCheck";
            var lblPageMin = CreateLabel("最小页数:", 280, 50, 100, 40);
            var numPageMin = CreateNumericUpDown(1, 9999, 390, 50, 100, 40);
            numPageMin.Name = "numPageMin";
            var lblPageMax = CreateLabel("最大页数:", 520, 50, 100, 40);
            var numPageMax = CreateNumericUpDown(1, 9999, 630, 50, 100, 40);
            numPageMax.Name = "numPageMax";
            var lblPageUnit = CreateLabel("页", 760, 50, 40, 40);

            grpPageCount.Controls.AddRange(new Control[] {
                chkPageCount, lblPageMin, numPageMin,
                lblPageMax, numPageMax, lblPageUnit
            });
            panel.Controls.Add(grpPageCount);
            yPos += 170;

            // 分组框：文件名非法词检查
            var grpFileNameIllegal = CreateGroupBox("文件名非法词检查", 20, yPos, 1200, 150);
            var chkFileNameIllegal = CreateCheckBox("启用文件名非法词检查", 30, 50, 240, 40);
            chkFileNameIllegal.Name = "chkFileNameIllegalCheck";
            var lblFileNameIllegal = CreateLabel("非法词数量:", 320, 50, 120, 40);
            var lblFileNameIllegalCount = CreateLabel("0个", 450, 50, 60, 40);
            lblFileNameIllegalCount.Name = "lblFileNameIllegalCount";
            lblFileNameIllegalCount.ForeColor = Color.Gray;
            var btnFileNameIllegal = CreateButton("设置非法词", 540, 50, 140, 40);
            btnFileNameIllegal.Name = "btnEditFileNameIllegalWords";

            grpFileNameIllegal.Controls.AddRange(new Control[] {
                chkFileNameIllegal, lblFileNameIllegal, lblFileNameIllegalCount, btnFileNameIllegal
            });
            panel.Controls.Add(grpFileNameIllegal);
            yPos += 170;

            // 分组框：内容非法词检查
            var grpContentIllegal = CreateGroupBox("内容非法词检查", 20, yPos, 1200, 150);
            var chkContentIllegal = CreateCheckBox("启用内容非法词检查", 30, 50, 240, 40);
            chkContentIllegal.Name = "chkContentIllegalCheck";
            var lblContentIllegal = CreateLabel("非法词数量:", 320, 50, 120, 40);
            var lblContentIllegalCount = CreateLabel("0个", 450, 50, 60, 40);
            lblContentIllegalCount.Name = "lblContentIllegalCount";
            lblContentIllegalCount.ForeColor = Color.Gray;
            var btnContentIllegal = CreateButton("设置非法词", 540, 50, 140, 40);
            btnContentIllegal.Name = "btnEditContentIllegalWords";

            grpContentIllegal.Controls.AddRange(new Control[] {
                chkContentIllegal, lblContentIllegal, lblContentIllegalCount, btnContentIllegal
            });
            panel.Controls.Add(grpContentIllegal);
        }

        /// <summary>
        /// 初始化内容删除设置标签页
        /// </summary>
        private void InitializeContentRemovalTab()
        {
            var panel = CreateScrollablePanel();
            tabPageContentRemoval.Controls.Add(panel);
            _tabPanels["ContentRemoval"] = panel;

            int yPos = 20;

            // 总开关 - 使用自动计算的尺寸
            var chkMasterSwitch = CreateCheckBox("启用内容删除功能", 20, yPos, 240, 40);
            chkMasterSwitch.Name = "chkContentRemovalMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 删除范围选择区域
            var grpDeletionScope = CreateDeletionScopeGroupBox(20, yPos, 1200, "ContentRemoval");
            panel.Controls.Add(grpDeletionScope);
            yPos += 120;

            // 幻灯片删除选项
            var grpSlideOptions = CreateGroupBox("幻灯片删除选项", 20, yPos, 1200, 680);

            var chkBlankSlides = CreateCheckBox("删除空白幻灯片", 30, 50, 200, 40);
            chkBlankSlides.Name = "chkDeleteBlankSlides";

            var chkSlidesWithoutText = CreateCheckBox("删除不包含任何文字内容的页（纯图片页）", 30, 100, 450, 40);
            chkSlidesWithoutText.Name = "chkDeleteSlidesWithoutText";

            var chkFirstSlide = CreateCheckBox("删除第一页", 30, 150, 150, 40);
            chkFirstSlide.Name = "chkDeleteFirstSlide";

            var chkLastSlide = CreateCheckBox("删除最后一页", 30, 200, 150, 40);
            chkLastSlide.Name = "chkDeleteLastSlide";

            var chkSlideRange = CreateCheckBox("删除指定页范围", 30, 250, 200, 40);
            chkSlideRange.Name = "chkDeleteSlideRange";
            var lblSlideRangeFrom = CreateLabel("从第", 280, 250, 60, 40);
            var numSlideRangeStart = CreateNumericUpDown(1, 9999, 350, 250, 100, 40);
            numSlideRangeStart.Name = "numSlideRangeStart";
            var lblSlideRangeTo = CreateLabel("页到第", 470, 250, 80, 40);
            var numSlideRangeEnd = CreateNumericUpDown(1, 9999, 560, 250, 100, 40);
            numSlideRangeEnd.Name = "numSlideRangeEnd";
            var lblSlideRangeEnd = CreateLabel("页", 680, 250, 40, 40);

            var chkKeywordSlides = CreateCheckBox("删除包含关键词的页", 30, 300, 240, 40);
            chkKeywordSlides.Name = "chkDeleteKeywordSlides";
            var txtSlideKeywords = CreateTextBox(300, 300, 860, 40);
            txtSlideKeywords.Name = "txtSlideKeywords";
            txtSlideKeywords.PlaceholderText = "输入关键词，用逗号分隔";

            var chkSpecificImageSlides = CreateCheckBox("删除包含指定图片的页", 30, 350, 240, 40);
            chkSpecificImageSlides.Name = "chkDeleteSpecificImageSlides";

            // 图片列表
            var lstSpecificImages = new ListView();
            lstSpecificImages.Name = "lstSpecificImages";
            lstSpecificImages.Location = new Point(30, 400);
            lstSpecificImages.Size = new Size(1130, 140);
            lstSpecificImages.View = View.Details;
            lstSpecificImages.FullRowSelect = true;
            lstSpecificImages.GridLines = true;
            lstSpecificImages.MultiSelect = false;
            lstSpecificImages.Font = new Font("Microsoft YaHei UI", 10F);
            lstSpecificImages.OwnerDraw = true; // 启用自定义绘制以实现表头文字居中

            // 添加列标题
            lstSpecificImages.Columns.Add("序号", 80, HorizontalAlignment.Center);
            lstSpecificImages.Columns.Add("图片文件名", 350, HorizontalAlignment.Center);
            lstSpecificImages.Columns.Add("文件路径", 700, HorizontalAlignment.Center);

            // 自定义绘制表头 - 水平垂直居中
            lstSpecificImages.DrawColumnHeader += (sender, e) =>
            {
                e.DrawBackground();
                var headerText = lstSpecificImages.Columns[e.ColumnIndex].Text;
                var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;
                TextRenderer.DrawText(e.Graphics, headerText, lstSpecificImages.Font, e.Bounds, Color.Black, textFlags);
            };

            // 自定义绘制子项 - 保持原有的对齐方式
            lstSpecificImages.DrawSubItem += (sender, e) =>
            {
                // 绘制背景
                if (e.Item?.Selected == true)
                {
                    e.Graphics.FillRectangle(SystemBrushes.Highlight, e.Bounds);
                }
                else
                {
                    e.Graphics.FillRectangle(SystemBrushes.Window, e.Bounds);
                }

                // 绘制边框
                e.Graphics.DrawRectangle(SystemPens.Control, e.Bounds);

                // 设置文字颜色
                var textColor = e.Item?.Selected == true ? SystemColors.HighlightText : SystemColors.WindowText;

                // 根据列的不同设置不同的对齐方式
                TextFormatFlags textFlags;
                if (e.ColumnIndex == 0) // 序号列 - 居中
                {
                    textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter | TextFormatFlags.SingleLine;
                }
                else // 文件名和路径列 - 左对齐
                {
                    textFlags = TextFormatFlags.Left | TextFormatFlags.VerticalCenter | TextFormatFlags.SingleLine;
                }

                // 绘制文字
                TextRenderer.DrawText(e.Graphics, e.SubItem?.Text ?? "", lstSpecificImages.Font, e.Bounds, textColor, textFlags);
            };

            // 自定义绘制项目 - 处理第一列（序号列）的居中显示
            lstSpecificImages.DrawItem += (sender, e) =>
            {
                // 只绘制第一列（序号列）
                if (e.Item != null)
                {
                    var bounds = e.Bounds;

                    // 计算第一列的边界
                    var firstColumnBounds = new Rectangle(bounds.X, bounds.Y, lstSpecificImages.Columns[0].Width, bounds.Height);

                    // 绘制背景
                    if ((e.State & ListViewItemStates.Selected) == ListViewItemStates.Selected)
                    {
                        e.Graphics.FillRectangle(SystemBrushes.Highlight, firstColumnBounds);
                    }
                    else
                    {
                        e.Graphics.FillRectangle(SystemBrushes.Window, firstColumnBounds);
                    }

                    // 绘制边框
                    e.Graphics.DrawRectangle(SystemPens.Control, firstColumnBounds);

                    // 设置文字颜色
                    var textColor = ((e.State & ListViewItemStates.Selected) == ListViewItemStates.Selected) ?
                        SystemColors.HighlightText : SystemColors.WindowText;

                    // 绘制序号文字 - 水平垂直居中
                    var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter | TextFormatFlags.SingleLine;
                    TextRenderer.DrawText(e.Graphics, e.Item.Text, lstSpecificImages.Font, firstColumnBounds, textColor, textFlags);
                }
            };

            // 图片管理按钮
            var btnAddImage = CreateButton("添加图片", 30, 560, 120, 40);
            btnAddImage.Name = "btnAddSpecificImage";
            btnAddImage.Font = new Font("Microsoft YaHei UI", 10F);

            var btnRemoveImage = CreateButton("删除图片", 170, 560, 120, 40);
            btnRemoveImage.Name = "btnRemoveSpecificImage";
            btnRemoveImage.Font = new Font("Microsoft YaHei UI", 10F);

            var btnClearImages = CreateButton("清空列表", 310, 560, 120, 40);
            btnClearImages.Name = "btnClearSpecificImages";
            btnClearImages.Font = new Font("Microsoft YaHei UI", 10F);

            grpSlideOptions.Controls.AddRange(new Control[] {
                chkBlankSlides, chkSlidesWithoutText, chkFirstSlide, chkLastSlide, chkSlideRange,
                lblSlideRangeFrom, numSlideRangeStart, lblSlideRangeTo, numSlideRangeEnd, lblSlideRangeEnd,
                chkKeywordSlides, txtSlideKeywords, chkSpecificImageSlides, lstSpecificImages,
                btnAddImage, btnRemoveImage, btnClearImages
            });
            panel.Controls.Add(grpSlideOptions);
            yPos += 700;

            // 段落和行删除选项
            var grpParagraphOptions = CreateGroupBox("段落和行删除选项", 20, yPos, 1200, 180);

            var chkBlankParagraphs = CreateCheckBox("删除空白段落", 30, 50, 200, 40);
            chkBlankParagraphs.Name = "chkDeleteBlankParagraphs";

            var chkBlankLines = CreateCheckBox("删除空白行", 30, 110, 200, 40);
            chkBlankLines.Name = "chkDeleteBlankLines";

            grpParagraphOptions.Controls.AddRange(new Control[] {
                chkBlankParagraphs, chkBlankLines
            });
            panel.Controls.Add(grpParagraphOptions);
        }

        /// <summary>
        /// 初始化文本删除标签页
        /// </summary>
        private void InitializeTextDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageTextDeletion.Controls.Add(panel);
            _tabPanels["TextDeletion"] = panel;

            int yPos = 20;

            // 总开关 - 使用自动计算的尺寸
            var chkMasterSwitch = CreateCheckBox("启用文本删除功能", 20, yPos, 240, 40);
            chkMasterSwitch.Name = "chkTextDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 删除范围选择区域
            var grpDeletionScope = CreateDeletionScopeGroupBox(20, yPos, 1200, "TextDeletion");
            panel.Controls.Add(grpDeletionScope);
            yPos += 120;

            // 基本文本删除选项
            var grpBasicTextOptions = CreateGroupBox("基本文本删除选项", 20, yPos, 1200, 300);

            var chkDeleteAllText = CreateCheckBox("删除所有文本", 30, 50, 180, 40);
            chkDeleteAllText.Name = "chkDeleteAllText";

            var chkDeleteTextBoxes = CreateCheckBox("删除文本框", 30, 100, 180, 40);
            chkDeleteTextBoxes.Name = "chkDeleteTextBoxes";

            var chkDeleteTitles = CreateCheckBox("删除标题", 30, 150, 180, 40);
            chkDeleteTitles.Name = "chkDeleteTitles";

            var chkDeleteBulletPoints = CreateCheckBox("删除项目符号", 30, 200, 180, 40);
            chkDeleteBulletPoints.Name = "chkDeleteBulletPoints";

            grpBasicTextOptions.Controls.AddRange(new Control[] {
                chkDeleteAllText, chkDeleteTextBoxes, chkDeleteTitles, chkDeleteBulletPoints
            });
            panel.Controls.Add(grpBasicTextOptions);
            yPos += 320;

            // 特定文本删除
            var grpSpecificTextDeletion = CreateGroupBox("删除特定文本内容", 20, yPos, 1200, 260);
            var chkDeleteSpecificText = CreateCheckBox("启用特定文本删除", 30, 50, 240, 40);
            chkDeleteSpecificText.Name = "chkDeleteSpecificText";
            var lblSpecificTextContent = CreateLabel("文本内容列表（一行一个）:", 30, 110, 280, 40);
            var txtSpecificTextContent = CreateTextBox(30, 160, 1140, 80, true);
            txtSpecificTextContent.Name = "txtSpecificTextContent";

            grpSpecificTextDeletion.Controls.AddRange(new Control[] {
                chkDeleteSpecificText, lblSpecificTextContent, txtSpecificTextContent
            });
            panel.Controls.Add(grpSpecificTextDeletion);
            yPos += 280;

            // 文本范围删除
            var grpTextRangeDeletion = CreateGroupBox("删除指定页范围的文本", 20, yPos, 1200, 150);
            var chkDeleteTextRange = CreateCheckBox("启用文本范围删除", 30, 50, 240, 40);
            chkDeleteTextRange.Name = "chkDeleteTextRange";
            var lblTextRangeFrom = CreateLabel("从第", 320, 50, 60, 40);
            var numTextRangeStart = CreateNumericUpDown(1, 9999, 390, 50, 100, 40);
            numTextRangeStart.Name = "numTextRangeStart";
            var lblTextRangeTo = CreateLabel("页到第", 510, 50, 80, 40);
            var numTextRangeEnd = CreateNumericUpDown(1, 9999, 600, 50, 100, 40);
            numTextRangeEnd.Name = "numTextRangeEnd";
            var lblTextRangeEndText = CreateLabel("页", 720, 50, 40, 40);

            grpTextRangeDeletion.Controls.AddRange(new Control[] {
                chkDeleteTextRange, lblTextRangeFrom, numTextRangeStart,
                lblTextRangeTo, numTextRangeEnd, lblTextRangeEndText
            });
            panel.Controls.Add(grpTextRangeDeletion);
            yPos += 170;

            // 段落删除
            var grpParagraphDeletion = CreateGroupBox("删除包含指定文本的段落", 20, yPos, 1200, 260);
            var chkParagraphDeletion = CreateCheckBox("启用段落删除", 30, 50, 200, 40);
            chkParagraphDeletion.Name = "chkDeleteParagraphsWithText";
            var lblParagraphKeywords = CreateLabel("关键词列表（一行一个）:", 30, 110, 280, 40);
            var txtParagraphKeywords = CreateTextBox(30, 160, 1140, 80, true);
            txtParagraphKeywords.Name = "txtParagraphKeywords";

            grpParagraphDeletion.Controls.AddRange(new Control[] {
                chkParagraphDeletion, lblParagraphKeywords, txtParagraphKeywords
            });
            panel.Controls.Add(grpParagraphDeletion);
            yPos += 280;

            // 文本框删除
            var grpTextBoxDeletion = CreateGroupBox("删除包含指定文本的文本框", 20, yPos, 1200, 260);
            var chkTextBoxDeletion = CreateCheckBox("启用文本框删除", 30, 50, 220, 40);
            chkTextBoxDeletion.Name = "chkDeleteTextBoxesWithText";
            var lblTextBoxKeywords = CreateLabel("关键词列表（一行一个）:", 30, 110, 280, 40);
            var txtTextBoxKeywords = CreateTextBox(30, 160, 1140, 80, true);
            txtTextBoxKeywords.Name = "txtTextBoxKeywords";

            grpTextBoxDeletion.Controls.AddRange(new Control[] {
                chkTextBoxDeletion, lblTextBoxKeywords, txtTextBoxKeywords
            });
            panel.Controls.Add(grpTextBoxDeletion);
            yPos += 280;

            // 表格删除
            var grpTableTextDeletion = CreateGroupBox("删除包含指定文本的表格", 20, yPos, 1200, 260);
            var chkTableTextDeletion = CreateCheckBox("启用表格删除", 30, 50, 200, 40);
            chkTableTextDeletion.Name = "chkDeleteTablesWithText";
            var lblTableKeywords = CreateLabel("关键词列表（一行一个）:", 30, 110, 280, 40);
            var txtTableKeywords = CreateTextBox(30, 160, 1140, 80, true);
            txtTableKeywords.Name = "txtTableKeywords";

            grpTableTextDeletion.Controls.AddRange(new Control[] {
                chkTableTextDeletion, lblTableKeywords, txtTableKeywords
            });
            panel.Controls.Add(grpTableTextDeletion);
        }

        /// <summary>
        /// 初始化图片删除标签页
        /// </summary>
        private void InitializeImageDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageImageDeletion.Controls.Add(panel);
            _tabPanels["ImageDeletion"] = panel;

            int yPos = 20;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用图片删除功能", 20, yPos, 240, 40);
            chkMasterSwitch.Name = "chkImageDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 删除范围选择区域
            var grpDeletionScope = CreateDeletionScopeGroupBox(20, yPos, 1200, "ImageDeletion");
            panel.Controls.Add(grpDeletionScope);
            yPos += 120;

            // 基本图片删除选项
            var grpBasicImageOptions = CreateGroupBox("基本图片删除选项", 20, yPos, 1200, 220);

            var chkAllImages = CreateCheckBox("删除所有图片", 30, 50, 180, 40);
            chkAllImages.Name = "chkDeleteAllImages";

            var chkSpecificImages = CreateCheckBox("删除指定图片", 30, 100, 180, 40);
            chkSpecificImages.Name = "chkDeleteSpecificImages";
            var txtSpecificImages = CreateTextBox(250, 100, 920, 40);
            txtSpecificImages.Name = "txtSpecificImageNames";
            txtSpecificImages.PlaceholderText = "输入图片名称，用逗号分隔";

            var chkLastSlideImages = CreateCheckBox("删除最后一页图片", 30, 150, 220, 40);
            chkLastSlideImages.Name = "chkDeleteLastSlideImages";

            var chkBackgroundImages = CreateCheckBox("删除背景图片", 300, 150, 180, 40);
            chkBackgroundImages.Name = "chkDeleteBackgroundImages";

            grpBasicImageOptions.Controls.AddRange(new Control[] {
                chkAllImages, chkSpecificImages, txtSpecificImages,
                chkLastSlideImages, chkBackgroundImages
            });
            panel.Controls.Add(grpBasicImageOptions);
            yPos += 240;

            // 页范围图片删除
            var grpImageRangeOptions = CreateGroupBox("页范围图片删除", 20, yPos, 1200, 140);

            var chkImageRange = CreateCheckBox("删除指定页范围的图片", 30, 50, 240, 40);
            chkImageRange.Name = "chkDeleteImageRange";
            var lblImageRangeFrom = CreateLabel("从第", 320, 50, 60, 40);
            var numImageRangeStart = CreateNumericUpDown(1, 9999, 390, 50, 100, 40);
            numImageRangeStart.Name = "numImageRangeStart";
            var lblImageRangeTo = CreateLabel("页到第", 510, 50, 80, 40);
            var numImageRangeEnd = CreateNumericUpDown(1, 9999, 600, 50, 100, 40);
            numImageRangeEnd.Name = "numImageRangeEnd";
            var lblImageRangeEndText = CreateLabel("页", 720, 50, 40, 40);

            grpImageRangeOptions.Controls.AddRange(new Control[] {
                chkImageRange, lblImageRangeFrom, numImageRangeStart,
                lblImageRangeTo, numImageRangeEnd, lblImageRangeEndText
            });
            panel.Controls.Add(grpImageRangeOptions);
            yPos += 160;

            // 位置条件图片删除 - 完全重写布局，按指定顺序排列
            var grpPositionImageOptions = CreateGroupBox("按位置删除图片（多区域支持）", 20, yPos, 1200, 780);

            // 1. 删除指定位置范围内的图片勾选框
            var chkPositionImages = CreateCheckBox("删除指定位置范围内的图片", 30, 50, 350, 40);
            chkPositionImages.Name = "chkDeletePositionImages";
            chkPositionImages.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Bold);

            // 2. 规则列表
            var lstRegions = new ListView();
            lstRegions.Name = "lstPositionRegions";
            lstRegions.Location = new Point(30, 100);
            lstRegions.Size = new Size(1130, 160);
            lstRegions.View = View.Details;
            lstRegions.FullRowSelect = true;
            lstRegions.GridLines = true;
            lstRegions.MultiSelect = false;
            lstRegions.Font = new Font("Microsoft YaHei UI", 10F);
            lstRegions.OwnerDraw = true; // 启用自定义绘制以实现文字居中

            // 添加列标题 - 将启用列改为序号列
            lstRegions.Columns.Add("序号", 90, HorizontalAlignment.Center);
            lstRegions.Columns.Add("区域名称", 200, HorizontalAlignment.Center);
            lstRegions.Columns.Add("左边距%", 200, HorizontalAlignment.Center);
            lstRegions.Columns.Add("上边距%", 200, HorizontalAlignment.Center);
            lstRegions.Columns.Add("宽度%", 200, HorizontalAlignment.Center);
            lstRegions.Columns.Add("高度%", 200, HorizontalAlignment.Center);

            // 自定义绘制表头 - 水平垂直居中
            lstRegions.DrawColumnHeader += (sender, e) =>
            {
                e.DrawBackground();
                var headerText = lstRegions.Columns[e.ColumnIndex].Text;
                var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;
                TextRenderer.DrawText(e.Graphics, headerText, lstRegions.Font, e.Bounds, Color.Black, textFlags);
            };

            // 自定义绘制子项 - 水平垂直居中
            lstRegions.DrawSubItem += (sender, e) =>
            {
                // 绘制背景
                if (e.Item?.Selected == true)
                {
                    e.Graphics.FillRectangle(SystemBrushes.Highlight, e.Bounds);
                }
                else
                {
                    e.Graphics.FillRectangle(SystemBrushes.Window, e.Bounds);
                }

                // 绘制边框
                e.Graphics.DrawRectangle(SystemPens.Control, e.Bounds);

                // 设置文字颜色
                var textColor = e.Item?.Selected == true ? SystemColors.HighlightText : SystemColors.WindowText;

                // 绘制文字 - 水平垂直居中
                var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter | TextFormatFlags.SingleLine;
                TextRenderer.DrawText(e.Graphics, e.SubItem?.Text ?? "", lstRegions.Font, e.Bounds, textColor, textFlags);
            };

            // 自定义绘制项目 - 处理第一列（序号列）的居中显示
            lstRegions.DrawItem += (sender, e) =>
            {
                // 只绘制第一列（序号列）
                if (e.Item != null)
                {
                    var bounds = e.Bounds;

                    // 计算第一列的边界
                    var firstColumnBounds = new Rectangle(bounds.X, bounds.Y, lstRegions.Columns[0].Width, bounds.Height);

                    // 绘制背景
                    if ((e.State & ListViewItemStates.Selected) == ListViewItemStates.Selected)
                    {
                        e.Graphics.FillRectangle(SystemBrushes.Highlight, firstColumnBounds);
                    }
                    else
                    {
                        e.Graphics.FillRectangle(SystemBrushes.Window, firstColumnBounds);
                    }

                    // 绘制边框
                    e.Graphics.DrawRectangle(SystemPens.Control, firstColumnBounds);

                    // 设置文字颜色
                    var textColor = ((e.State & ListViewItemStates.Selected) == ListViewItemStates.Selected) ?
                        SystemColors.HighlightText : SystemColors.WindowText;

                    // 绘制序号文字 - 水平垂直居中
                    var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter | TextFormatFlags.SingleLine;
                    TextRenderer.DrawText(e.Graphics, e.Item.Text, lstRegions.Font, firstColumnBounds, textColor, textFlags);
                }
            };

            // 3. 新增区域、编辑区域、删除区域按钮
            var btnAddRegion = CreateButton("新增区域", 30, 280, 140, 40);
            btnAddRegion.Name = "btnAddPositionRegion";
            btnAddRegion.Font = new Font("Microsoft YaHei UI", 10F);

            var btnEditRegion = CreateButton("编辑区域", 190, 280, 140, 40);
            btnEditRegion.Name = "btnEditPositionRegion";
            btnEditRegion.Font = new Font("Microsoft YaHei UI", 10F);

            var btnDeleteRegion = CreateButton("删除区域", 350, 280, 140, 40);
            btnDeleteRegion.Name = "btnDeletePositionRegion";
            btnDeleteRegion.Font = new Font("Microsoft YaHei UI", 10F);

            // 4. 左边距及输入框、上边距及输入框、宽度及输入框、高度及输入框
            var lblRectX = CreateLabel("左边距%:", 30, 340, 100, 40);
            lblRectX.Font = new Font("Microsoft YaHei UI", 10F);
            lblRectX.TextAlign = ContentAlignment.MiddleLeft;
            var numRectX = CreateNumericUpDown(0, 115, 140, 340, 100, 40);
            numRectX.Name = "numPositionXPercent";
            numRectX.DecimalPlaces = 1;
            numRectX.Font = new Font("Microsoft YaHei UI", 10F);

            var lblRectY = CreateLabel("上边距%:", 260, 340, 100, 40);
            lblRectY.Font = new Font("Microsoft YaHei UI", 10F);
            lblRectY.TextAlign = ContentAlignment.MiddleLeft;
            var numRectY = CreateNumericUpDown(0, 100, 370, 340, 100, 40);
            numRectY.Name = "numPositionYPercent";
            numRectY.DecimalPlaces = 1;
            numRectY.Font = new Font("Microsoft YaHei UI", 10F);

            var lblRectWidth = CreateLabel("宽度%:", 490, 340, 80, 40);
            lblRectWidth.Font = new Font("Microsoft YaHei UI", 10F);
            lblRectWidth.TextAlign = ContentAlignment.MiddleLeft;
            var numRectWidth = CreateNumericUpDown(1, 100, 580, 340, 100, 40);
            numRectWidth.Name = "numPositionWidthPercent";
            numRectWidth.DecimalPlaces = 1;
            numRectWidth.Font = new Font("Microsoft YaHei UI", 10F);

            var lblRectHeight = CreateLabel("高度%:", 700, 340, 80, 40);
            lblRectHeight.Font = new Font("Microsoft YaHei UI", 10F);
            lblRectHeight.TextAlign = ContentAlignment.MiddleLeft;
            var numRectHeight = CreateNumericUpDown(1, 100, 790, 340, 100, 40);
            numRectHeight.Name = "numPositionHeightPercent";
            numRectHeight.DecimalPlaces = 1;
            numRectHeight.Font = new Font("Microsoft YaHei UI", 10F);

            // 5. 保存、取消、区域名称及输入框、区域助手
            var btnSaveRegion = CreateButton("保存", 30, 400, 100, 40);
            btnSaveRegion.Name = "btnSavePositionRegion";
            btnSaveRegion.Font = new Font("Microsoft YaHei UI", 10F);

            var btnCancelEdit = CreateButton("取消", 150, 400, 100, 40);
            btnCancelEdit.Name = "btnCancelEditRegion";
            btnCancelEdit.Font = new Font("Microsoft YaHei UI", 10F);

            var lblRegionName = CreateLabel("区域名称:", 270, 400, 105, 40);
            lblRegionName.Font = new Font("Microsoft YaHei UI", 10F);
            lblRegionName.TextAlign = ContentAlignment.MiddleLeft;
            var txtRegionName = CreateTextBox(385, 400, 300, 40);
            txtRegionName.Name = "txtRegionName";
            txtRegionName.PlaceholderText = "区域名称";
            txtRegionName.Font = new Font("Microsoft YaHei UI", 10F);
            txtRegionName.TextAlign = HorizontalAlignment.Center;

            var btnRegionHelper = CreateButton("区域助手", 705, 400, 140, 40);
            btnRegionHelper.Name = "btnPositionRegionHelper";
            btnRegionHelper.Font = new Font("Microsoft YaHei UI", 10F);

            // 6. 使用说明
            var lblExample = CreateLabel("使用说明: 可添加多个删除区域，只有完全位于区域内的图片才会被删除", 30, 460, 1130, 40);
            lblExample.ForeColor = Color.DarkBlue;
            lblExample.AutoSize = false;
            lblExample.Font = new Font("Microsoft YaHei UI", 10F);

            // 7. 常用示例
            var lblExamples1 = CreateLabel("常用示例:", 30, 510, 100, 40);
            lblExamples1.ForeColor = Color.Gray;
            lblExamples1.AutoSize = false;
            lblExamples1.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Bold);

            var lblExamples2 = CreateLabel("• 删除左上角Logo: 左边距0%, 上边距0%, 宽度20%, 高度15%", 30, 550, 1130, 40);
            lblExamples2.ForeColor = Color.Gray;
            lblExamples2.AutoSize = false;
            lblExamples2.Font = new Font("Microsoft YaHei UI", 10F);

            var lblExamples3 = CreateLabel("• 删除右下角装饰: 左边距80%, 上边距85%, 宽度20%, 高度15%", 30, 590, 1130, 40);
            lblExamples3.ForeColor = Color.Gray;
            lblExamples3.AutoSize = false;
            lblExamples3.Font = new Font("Microsoft YaHei UI", 10F);

            var lblExamples4 = CreateLabel("• 删除底部水印: 左边距0%, 上边距90%, 宽度100%, 高度10%", 30, 630, 1130, 40);
            lblExamples4.ForeColor = Color.Gray;
            lblExamples4.AutoSize = false;
            lblExamples4.Font = new Font("Microsoft YaHei UI", 10F);

            grpPositionImageOptions.Controls.AddRange(new Control[] {
                chkPositionImages, lstRegions,
                btnAddRegion, btnEditRegion, btnDeleteRegion, btnRegionHelper,
                lblRegionName, txtRegionName, lblRectX, numRectX, lblRectY, numRectY,
                lblRectWidth, numRectWidth, lblRectHeight, numRectHeight,
                btnSaveRegion, btnCancelEdit, lblExample,
                lblExamples1, lblExamples2, lblExamples3, lblExamples4
            });
            panel.Controls.Add(grpPositionImageOptions);
            yPos += 770;
        }

        /// <summary>
        /// 初始化表格删除标签页
        /// </summary>
        private void InitializeTableDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageTableDeletion.Controls.Add(panel);
            _tabPanels["TableDeletion"] = panel;

            int yPos = 20;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用表格删除功能", 20, yPos, 240, 40);
            chkMasterSwitch.Name = "chkTableDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 删除范围选择区域
            var grpDeletionScope = CreateDeletionScopeGroupBox(20, yPos, 1200, "TableDeletion");
            panel.Controls.Add(grpDeletionScope);
            yPos += 120;

            // 表格删除选项
            var grpTableOptions = CreateGroupBox("表格删除选项", 20, yPos, 1200, 200);

            var chkAllTables = CreateCheckBox("删除所有表格", 30, 50, 180, 40);
            chkAllTables.Name = "chkDeleteAllTables";

            var chkLastSlideTables = CreateCheckBox("删除最后一页表格", 30, 100, 220, 40);
            chkLastSlideTables.Name = "chkDeleteLastSlideTables";

            var chkTableRange = CreateCheckBox("删除指定页范围的表格", 30, 150, 240, 40);
            chkTableRange.Name = "chkDeleteTableRange";
            var lblTableRangeFrom = CreateLabel("从第", 320, 150, 60, 40);
            var numTableRangeStart = CreateNumericUpDown(1, 9999, 390, 150, 100, 40);
            numTableRangeStart.Name = "numTableRangeStart";
            var lblTableRangeTo = CreateLabel("页到第", 510, 150, 80, 40);
            var numTableRangeEnd = CreateNumericUpDown(1, 9999, 600, 150, 100, 40);
            numTableRangeEnd.Name = "numTableRangeEnd";
            var lblTableRangeEndText = CreateLabel("页", 720, 150, 40, 40);

            grpTableOptions.Controls.AddRange(new Control[] {
                chkAllTables, chkLastSlideTables, chkTableRange,
                lblTableRangeFrom, numTableRangeStart, lblTableRangeTo,
                numTableRangeEnd, lblTableRangeEndText
            });
            panel.Controls.Add(grpTableOptions);
        }

        /// <summary>
        /// 初始化图表删除标签页
        /// </summary>
        private void InitializeChartDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageChartDeletion.Controls.Add(panel);
            _tabPanels["ChartDeletion"] = panel;

            int yPos = 20;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用图表删除功能", 20, yPos, 240, 40);
            chkMasterSwitch.Name = "chkChartDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 删除范围选择区域
            var grpDeletionScope = CreateDeletionScopeGroupBox(20, yPos, 1200, "ChartDeletion");
            panel.Controls.Add(grpDeletionScope);
            yPos += 120;

            // 图表删除选项
            var grpChartOptions = CreateGroupBox("图表删除选项", 20, yPos, 1200, 260);

            var chkAllCharts = CreateCheckBox("删除所有图表", 30, 50, 180, 40);
            chkAllCharts.Name = "chkDeleteAllCharts";

            var chkLastSlideCharts = CreateCheckBox("删除最后一页图表", 30, 100, 220, 40);
            chkLastSlideCharts.Name = "chkDeleteLastSlideCharts";

            var chkChartRange = CreateCheckBox("删除指定页范围的图表", 30, 150, 240, 40);
            chkChartRange.Name = "chkDeleteChartRange";
            var lblChartRangeFrom = CreateLabel("从第", 320, 150, 60, 40);
            var numChartRangeStart = CreateNumericUpDown(1, 9999, 390, 150, 100, 40);
            numChartRangeStart.Name = "numChartRangeStart";
            var lblChartRangeTo = CreateLabel("页到第", 510, 150, 80, 40);
            var numChartRangeEnd = CreateNumericUpDown(1, 9999, 600, 150, 100, 40);
            numChartRangeEnd.Name = "numChartRangeEnd";
            var lblChartRangeEndText = CreateLabel("页", 720, 150, 40, 40);

            // 删除包含指定文本的图表
            var chkChartsWithText = CreateCheckBox("删除包含指定文本的图表", 30, 200, 280, 40);
            chkChartsWithText.Name = "chkDeleteChartsWithText";
            var btnEditChartKeywords = CreateButton("编辑关键词", 350, 200, 120, 40);
            btnEditChartKeywords.Name = "btnEditChartKeywords";
            var lblChartKeywordsCount = CreateLabel("(0个关键词)", 490, 200, 120, 40);
            lblChartKeywordsCount.Name = "lblChartKeywordsCount";

            grpChartOptions.Controls.AddRange(new Control[] {
                chkAllCharts, chkLastSlideCharts, chkChartRange,
                lblChartRangeFrom, numChartRangeStart, lblChartRangeTo,
                numChartRangeEnd, lblChartRangeEndText,
                chkChartsWithText, btnEditChartKeywords, lblChartKeywordsCount
            });
            panel.Controls.Add(grpChartOptions);
        }

        /// <summary>
        /// 初始化音频视频删除标签页
        /// </summary>
        private void InitializeMediaDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageMediaDeletion.Controls.Add(panel);
            _tabPanels["MediaDeletion"] = panel;

            int yPos = 20;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用音频视频删除功能", 20, yPos, 280, 40);
            chkMasterSwitch.Name = "chkMediaDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 删除范围选择区域
            var grpDeletionScope = CreateDeletionScopeGroupBox(20, yPos, 1200, "MediaDeletion");
            panel.Controls.Add(grpDeletionScope);
            yPos += 120;

            // 音频删除选项
            var grpAudioOptions = CreateGroupBox("音频删除选项", 20, yPos, 1200, 200);

            var chkAllAudio = CreateCheckBox("删除所有音频", 30, 50, 180, 40);
            chkAllAudio.Name = "chkDeleteAllAudio";

            var chkLastSlideAudio = CreateCheckBox("删除最后一页音频", 30, 100, 220, 40);
            chkLastSlideAudio.Name = "chkDeleteLastSlideAudio";

            var chkAudioRange = CreateCheckBox("删除指定页范围的音频", 30, 150, 240, 40);
            chkAudioRange.Name = "chkDeleteAudioRange";
            var lblAudioRangeFrom = CreateLabel("从第", 320, 150, 60, 40);
            var numAudioRangeStart = CreateNumericUpDown(1, 9999, 390, 150, 100, 40);
            numAudioRangeStart.Name = "numAudioRangeStart";
            var lblAudioRangeTo = CreateLabel("页到第", 510, 150, 80, 40);
            var numAudioRangeEnd = CreateNumericUpDown(1, 9999, 600, 150, 100, 40);
            numAudioRangeEnd.Name = "numAudioRangeEnd";
            var lblAudioRangeEndText = CreateLabel("页", 720, 150, 40, 40);

            grpAudioOptions.Controls.AddRange(new Control[] {
                chkAllAudio, chkLastSlideAudio, chkAudioRange,
                lblAudioRangeFrom, numAudioRangeStart, lblAudioRangeTo,
                numAudioRangeEnd, lblAudioRangeEndText
            });
            panel.Controls.Add(grpAudioOptions);
            yPos += 220;

            // 视频删除选项
            var grpVideoOptions = CreateGroupBox("视频删除选项", 20, yPos, 1200, 200);

            var chkAllVideo = CreateCheckBox("删除所有视频", 30, 50, 180, 40);
            chkAllVideo.Name = "chkDeleteAllVideo";

            var chkLastSlideVideo = CreateCheckBox("删除最后一页视频", 30, 100, 220, 40);
            chkLastSlideVideo.Name = "chkDeleteLastSlideVideo";

            var chkVideoRange = CreateCheckBox("删除指定页范围的视频", 30, 150, 240, 40);
            chkVideoRange.Name = "chkDeleteVideoRange";
            var lblVideoRangeFrom = CreateLabel("从第", 320, 150, 60, 40);
            var numVideoRangeStart = CreateNumericUpDown(1, 9999, 390, 150, 100, 40);
            numVideoRangeStart.Name = "numVideoRangeStart";
            var lblVideoRangeTo = CreateLabel("页到第", 510, 150, 80, 40);
            var numVideoRangeEnd = CreateNumericUpDown(1, 9999, 600, 150, 100, 40);
            numVideoRangeEnd.Name = "numVideoRangeEnd";
            var lblVideoRangeEndText = CreateLabel("页", 720, 150, 40, 40);

            grpVideoOptions.Controls.AddRange(new Control[] {
                chkAllVideo, chkLastSlideVideo, chkVideoRange,
                lblVideoRangeFrom, numVideoRangeStart, lblVideoRangeTo,
                numVideoRangeEnd, lblVideoRangeEndText
            });
            panel.Controls.Add(grpVideoOptions);
        }

        /// <summary>
        /// 初始化联系方式删除标签页
        /// </summary>
        private void InitializeContactDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageContactDeletion.Controls.Add(panel);
            _tabPanels["ContactDeletion"] = panel;

            int yPos = 20;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用联系方式删除功能", 20, yPos, 280, 40);
            chkMasterSwitch.Name = "chkContactDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 删除范围选择区域
            var grpDeletionScope = CreateDeletionScopeGroupBox(20, yPos, 1200, "ContactDeletion");
            panel.Controls.Add(grpDeletionScope);
            yPos += 120;

            // 联系方式删除选项
            var grpContactOptions = CreateGroupBox("联系方式删除选项", 20, yPos, 1200, 300);

            var chkPhoneNumbers = CreateCheckBox("删除包含手机号码的文本", 30, 50, 280, 40);
            chkPhoneNumbers.Name = "chkDeletePhoneNumbers";

            var chkLandlineNumbers = CreateCheckBox("删除包含固定电话的文本", 30, 100, 280, 40);
            chkLandlineNumbers.Name = "chkDeleteLandlineNumbers";

            var chkEmailAddresses = CreateCheckBox("删除包含邮箱地址的文本", 30, 150, 280, 40);
            chkEmailAddresses.Name = "chkDeleteEmailAddresses";

            var chkWebsites = CreateCheckBox("删除包含网址的文本", 350, 50, 240, 40);
            chkWebsites.Name = "chkDeleteWebsites";

            var chkHyperlinks = CreateCheckBox("删除所有超链接", 350, 100, 200, 40);
            chkHyperlinks.Name = "chkDeleteHyperlinks";

            grpContactOptions.Controls.AddRange(new Control[] {
                chkPhoneNumbers, chkLandlineNumbers, chkEmailAddresses,
                chkWebsites, chkHyperlinks
            });
            panel.Controls.Add(grpContactOptions);
        }

        /// <summary>
        /// 初始化动画删除标签页
        /// </summary>
        private void InitializeAnimationDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageAnimationDeletion.Controls.Add(panel);
            _tabPanels["AnimationDeletion"] = panel;

            int yPos = 20;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用动画删除功能", 20, yPos, 240, 40);
            chkMasterSwitch.Name = "chkAnimationDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 删除范围选择区域
            var grpDeletionScope = CreateDeletionScopeGroupBox(20, yPos, 1200, "AnimationDeletion");
            panel.Controls.Add(grpDeletionScope);
            yPos += 120;

            // 动画效果删除选项
            var grpAnimationOptions = CreateGroupBox("动画效果删除选项", 20, yPos, 1200, 200);

            var chkAllAnimations = CreateCheckBox("删除所有动画效果", 30, 50, 220, 40);
            chkAllAnimations.Name = "chkDeleteAllAnimations";

            var chkLastSlideAnimations = CreateCheckBox("删除最后一页动画效果", 30, 100, 260, 40);
            chkLastSlideAnimations.Name = "chkDeleteLastSlideAnimations";

            var chkAnimationRange = CreateCheckBox("删除指定页范围的动画效果", 30, 150, 280, 40);
            chkAnimationRange.Name = "chkDeleteAnimationRange";
            var lblAnimationRangeFrom = CreateLabel("从第", 350, 150, 60, 40);
            var numAnimationRangeStart = CreateNumericUpDown(1, 9999, 420, 150, 100, 40);
            numAnimationRangeStart.Name = "numAnimationRangeStart";
            var lblAnimationRangeTo = CreateLabel("页到第", 540, 150, 80, 40);
            var numAnimationRangeEnd = CreateNumericUpDown(1, 9999, 630, 150, 100, 40);
            numAnimationRangeEnd.Name = "numAnimationRangeEnd";
            var lblAnimationRangeEndText = CreateLabel("页", 750, 150, 40, 40);

            grpAnimationOptions.Controls.AddRange(new Control[] {
                chkAllAnimations, chkLastSlideAnimations, chkAnimationRange,
                lblAnimationRangeFrom, numAnimationRangeStart, lblAnimationRangeTo,
                numAnimationRangeEnd, lblAnimationRangeEndText
            });
            panel.Controls.Add(grpAnimationOptions);
            yPos += 220;

            // 切换效果删除选项
            var grpTransitionOptions = CreateGroupBox("切换效果删除选项", 20, yPos, 1200, 200);

            var chkAllTransitions = CreateCheckBox("删除所有切换效果", 30, 50, 220, 40);
            chkAllTransitions.Name = "chkDeleteAllTransitions";

            var chkLastSlideTransitions = CreateCheckBox("删除最后一页切换效果", 30, 100, 260, 40);
            chkLastSlideTransitions.Name = "chkDeleteLastSlideTransitions";

            var chkTransitionRange = CreateCheckBox("删除指定页范围的切换效果", 30, 150, 280, 40);
            chkTransitionRange.Name = "chkDeleteTransitionRange";
            var lblTransitionRangeFrom = CreateLabel("从第", 350, 150, 60, 40);
            var numTransitionRangeStart = CreateNumericUpDown(1, 9999, 420, 150, 100, 40);
            numTransitionRangeStart.Name = "numTransitionRangeStart";
            var lblTransitionRangeTo = CreateLabel("页到第", 540, 150, 80, 40);
            var numTransitionRangeEnd = CreateNumericUpDown(1, 9999, 630, 150, 100, 40);
            numTransitionRangeEnd.Name = "numTransitionRangeEnd";
            var lblTransitionRangeEndText = CreateLabel("页", 750, 150, 40, 40);

            grpTransitionOptions.Controls.AddRange(new Control[] {
                chkAllTransitions, chkLastSlideTransitions, chkTransitionRange,
                lblTransitionRangeFrom, numTransitionRangeStart, lblTransitionRangeTo,
                numTransitionRangeEnd, lblTransitionRangeEndText
            });
            panel.Controls.Add(grpTransitionOptions);
        }

        /// <summary>
        /// 初始化备注删除标签页
        /// </summary>
        private void InitializeNotesDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageNotesDeletion.Controls.Add(panel);
            _tabPanels["NotesDeletion"] = panel;

            int yPos = 20;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用备注删除功能", 20, yPos, 240, 40);
            chkMasterSwitch.Name = "chkNotesDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 删除范围选择区域
            var grpDeletionScope = CreateDeletionScopeGroupBox(20, yPos, 1200, "NotesDeletion");
            panel.Controls.Add(grpDeletionScope);
            yPos += 120;

            // 备注删除选项
            var grpNotesOptions = CreateGroupBox("备注删除选项", 20, yPos, 1200, 160);

            var chkDeleteSlideNotes = CreateCheckBox("删除幻灯片备注", 30, 50, 200, 40);
            chkDeleteSlideNotes.Name = "chkDeleteSlideNotes";

            var chkClearNotesContent = CreateCheckBox("清空备注内容", 30, 100, 200, 40);
            chkClearNotesContent.Name = "chkClearNotesContent";

            grpNotesOptions.Controls.AddRange(new Control[] {
                chkDeleteSlideNotes, chkClearNotesContent
            });
            panel.Controls.Add(grpNotesOptions);
        }

        /// <summary>
        /// 初始化格式删除标签页
        /// </summary>
        private void InitializeFormatDeletionTab()
        {
            var panel = CreateScrollablePanel();
            tabPageFormatDeletion.Controls.Add(panel);
            _tabPanels["FormatDeletion"] = panel;

            int yPos = 20;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用格式删除功能", 20, yPos, 240, 40);
            chkMasterSwitch.Name = "chkFormatDeletionMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 删除范围选择区域
            var grpDeletionScope = CreateDeletionScopeGroupBox(20, yPos, 1200, "FormatDeletion");
            panel.Controls.Add(grpDeletionScope);
            yPos += 120;

            // 文本格式删除选项
            var grpTextFormatOptions = CreateGroupBox("文本格式删除选项", 20, yPos, 1200, 160);

            var chkDeleteFontFormatting = CreateCheckBox("删除字体格式（字体、大小、颜色等）", 30, 50, 450, 40);
            chkDeleteFontFormatting.Name = "chkDeleteFontFormatting";

            var chkDeleteParagraphFormatting = CreateCheckBox("删除段落格式（对齐、缩进、行距等）", 30, 100, 450, 40);
            chkDeleteParagraphFormatting.Name = "chkDeleteParagraphFormatting";

            var chkDeleteListFormatting = CreateCheckBox("删除列表格式（项目符号、编号等）", 520, 50, 450, 40);
            chkDeleteListFormatting.Name = "chkDeleteListFormatting";

            grpTextFormatOptions.Controls.AddRange(new Control[] {
                chkDeleteFontFormatting, chkDeleteParagraphFormatting, chkDeleteListFormatting
            });
            panel.Controls.Add(grpTextFormatOptions);
            yPos += 180;

            // 对象格式删除选项
            var grpObjectFormatOptions = CreateGroupBox("对象格式删除选项", 20, yPos, 1200, 160);

            var chkDeleteTableFormatting = CreateCheckBox("删除表格格式（边框、填充、样式等）", 30, 50, 450, 40);
            chkDeleteTableFormatting.Name = "chkDeleteTableFormatting";

            var chkDeleteShapeFormatting = CreateCheckBox("删除形状格式（填充、边框、效果等）", 30, 100, 450, 40);
            chkDeleteShapeFormatting.Name = "chkDeleteShapeFormatting";

            var chkDeleteImageFormatting = CreateCheckBox("删除图片格式（边框、效果、调整等）", 520, 50, 450, 40);
            chkDeleteImageFormatting.Name = "chkDeleteImageFormatting";

            var chkDeleteChartFormatting = CreateCheckBox("删除图表格式（样式、颜色、效果等）", 520, 100, 450, 40);
            chkDeleteChartFormatting.Name = "chkDeleteChartFormatting";

            grpObjectFormatOptions.Controls.AddRange(new Control[] {
                chkDeleteTableFormatting, chkDeleteShapeFormatting,
                chkDeleteImageFormatting, chkDeleteChartFormatting
            });
            panel.Controls.Add(grpObjectFormatOptions);
            yPos += 180;

            // 背景格式删除选项
            var grpBackgroundFormatOptions = CreateGroupBox("背景格式删除选项", 20, yPos, 1200, 120);

            var chkDeleteBackgroundFormatting = CreateCheckBox("删除背景格式（背景色、图片、渐变等）", 30, 50, 450, 40);
            chkDeleteBackgroundFormatting.Name = "chkDeleteBackgroundFormatting";

            grpBackgroundFormatOptions.Controls.Add(chkDeleteBackgroundFormatting);
            panel.Controls.Add(grpBackgroundFormatOptions);
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // 按钮事件
                btnOK.Click += BtnOK_Click;
                btnCancel.Click += BtnCancel_Click;

                // 窗体事件
                this.Load += ContentDeletionForm_Load;
                this.FormClosing += ContentDeletionForm_FormClosing;

                // 非法词按钮事件
                SetupIllegalWordsButtonEvents();

                // 位置区域管理事件
                SetupPositionRegionEvents();

                // 图片管理按钮事件
                SetupImageManagementButtonEvents();

                // 图表关键词按钮事件
                SetupChartKeywordsButtonEvents();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置事件处理器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置非法词按钮事件
        /// </summary>
        private void SetupIllegalWordsButtonEvents()
        {
            try
            {
                // 文件名非法词按钮
                var btnFileNameIllegal = FindControlByName(_tabPanels["DocumentDeletion"], "btnEditFileNameIllegalWords") as Button;
                if (btnFileNameIllegal != null)
                {
                    btnFileNameIllegal.Click += BtnFileNameIllegalWords_Click;
                }

                // 内容非法词按钮
                var btnContentIllegal = FindControlByName(_tabPanels["DocumentDeletion"], "btnEditContentIllegalWords") as Button;
                if (btnContentIllegal != null)
                {
                    btnContentIllegal.Click += BtnContentIllegalWords_Click;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置非法词按钮事件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载设置到界面
        /// </summary>
        private void LoadSettingsToUI()
        {
            try
            {
                // 加载删除文档设置
                LoadDocumentDeletionSettings();

                // 加载内容删除设置
                LoadContentRemovalSettings();

                // 加载文本删除设置
                LoadTextDeletionSettings();

                // 加载图片删除设置
                LoadImageDeletionSettings();

                // 加载表格删除设置
                LoadTableDeletionSettings();

                // 加载图表删除设置
                LoadChartDeletionSettings();

                // 加载音频视频删除设置
                LoadMediaDeletionSettings();

                // 加载联系方式删除设置
                LoadContactDeletionSettings();

                // 加载动画删除设置
                LoadAnimationDeletionSettings();

                // 加载备注删除设置
                LoadNotesDeletionSettings();

                // 加载格式删除设置
                LoadFormatDeletionSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置到界面失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载删除文档设置
        /// </summary>
        private void LoadDocumentDeletionSettings()
        {
            var settings = _currentSettings.DocumentDeletion;
            var panel = _tabPanels["DocumentDeletion"];

            // 总开关
            SetCheckBoxValue(panel, "chkDocumentDeletionMaster", settings.EnableDocumentDeletion);

            // 删除范围设置
            SetDeletionScope(panel, "DocumentDeletion", settings.DeletionScope);

            // 文件名长度检查
            SetCheckBoxValue(panel, "chkFileNameLengthCheck", settings.EnableFileNameLengthCheck);
            SetNumericUpDownValue(panel, "numFileNameMinLength", settings.FileNameMinLength);
            SetNumericUpDownValue(panel, "numFileNameMaxLength", settings.FileNameMaxLength);

            // 文件大小检查
            SetCheckBoxValue(panel, "chkFileSizeCheck", settings.EnableFileSizeCheck);
            SetNumericUpDownValue(panel, "numFileSizeMin", settings.FileMinSize);
            SetNumericUpDownValue(panel, "numFileSizeMax", settings.FileMaxSize);
            SetComboBoxValue(panel, "cmbFileSizeMinUnit", settings.FileMinSizeUnit);
            SetComboBoxValue(panel, "cmbFileSizeMaxUnit", settings.FileMaxSizeUnit);

            // 内容字符数检查
            SetCheckBoxValue(panel, "chkContentCharCountCheck", settings.EnableContentCharCountCheck);
            SetNumericUpDownValue(panel, "numContentCharMin", settings.ContentMinCharCount);
            SetNumericUpDownValue(panel, "numContentCharMax", settings.ContentMaxCharCount);

            // 页数检查
            SetCheckBoxValue(panel, "chkPageCountCheck", settings.EnablePageCountCheck);
            SetNumericUpDownValue(panel, "numPageMin", settings.MinPageCount);
            SetNumericUpDownValue(panel, "numPageMax", settings.MaxPageCount);

            // 非法词检查
            SetCheckBoxValue(panel, "chkFileNameIllegalCheck", settings.EnableFileNameIllegalWordsCheck);
            UpdateIllegalWordsCountLabel("lblFileNameIllegalCount", settings.FileNameIllegalWords.Count);
            SetCheckBoxValue(panel, "chkContentIllegalCheck", settings.EnableContentIllegalWordsCheck);
            UpdateIllegalWordsCountLabel("lblContentIllegalCount", settings.ContentIllegalWords.Count);
        }

        /// <summary>
        /// 加载内容删除设置
        /// </summary>
        private void LoadContentRemovalSettings()
        {
            var settings = _currentSettings.ContentRemoval;
            var panel = _tabPanels["ContentRemoval"];

            // 总开关
            SetCheckBoxValue(panel, "chkContentRemovalMaster", settings.EnableContentRemoval);

            // 删除范围设置
            SetDeletionScope(panel, "ContentRemoval", settings.DeletionScope);

            SetCheckBoxValue(panel, "chkDeleteBlankSlides", settings.DeleteBlankSlides);
            SetCheckBoxValue(panel, "chkDeleteSlidesWithoutText", settings.DeleteSlidesWithoutText);
            SetCheckBoxValue(panel, "chkDeleteFirstSlide", settings.DeleteFirstSlide);
            SetCheckBoxValue(panel, "chkDeleteLastSlide", settings.DeleteLastSlide);
            SetCheckBoxValue(panel, "chkDeleteSlideRange", settings.EnableSlideRangeDeletion);
            SetNumericUpDownValue(panel, "numSlideRangeStart", settings.SlideRangeStart);
            SetNumericUpDownValue(panel, "numSlideRangeEnd", settings.SlideRangeEnd);
            SetCheckBoxValue(panel, "chkDeleteKeywordSlides", settings.EnableKeywordSlidesDeletion);
            SetTextBoxValue(panel, "txtSlideKeywords", string.Join(",", settings.SlideKeywords));
            SetCheckBoxValue(panel, "chkDeleteSpecificImageSlides", settings.EnableSpecificImageSlidesDeletion);
            LoadSpecificImagesList(panel, settings.SpecificImageNames);
            SetCheckBoxValue(panel, "chkDeleteBlankParagraphs", settings.DeleteBlankParagraphs);
            SetCheckBoxValue(panel, "chkDeleteBlankLines", settings.DeleteBlankLines);
        }

        /// <summary>
        /// 加载文本删除设置
        /// </summary>
        private void LoadTextDeletionSettings()
        {
            var settings = _currentSettings.TextDeletion;
            var panel = _tabPanels["TextDeletion"];

            // 总开关
            SetCheckBoxValue(panel, "chkTextDeletionMaster", settings.EnableTextDeletion);

            // 删除范围设置
            SetDeletionScope(panel, "TextDeletion", settings.DeletionScope);

            // 新增的文本删除功能
            SetCheckBoxValue(panel, "chkDeleteAllText", settings.DeleteAllText);
            SetCheckBoxValue(panel, "chkDeleteTextBoxes", settings.DeleteTextBoxes);
            SetCheckBoxValue(panel, "chkDeleteTitles", settings.DeleteTitles);
            SetCheckBoxValue(panel, "chkDeleteBulletPoints", settings.DeleteBulletPoints);

            // 删除特定文本
            SetCheckBoxValue(panel, "chkDeleteSpecificText", settings.DeleteSpecificText);
            SetTextBoxValue(panel, "txtSpecificTextContent", string.Join("\r\n", settings.SpecificTextContent));

            // 删除文本范围
            SetCheckBoxValue(panel, "chkDeleteTextRange", settings.DeleteTextRange);
            SetNumericUpDownValue(panel, "numTextRangeStart", settings.TextSlideRangeStart);
            SetNumericUpDownValue(panel, "numTextRangeEnd", settings.TextSlideRangeEnd);

            // 保留原有的功能（向后兼容）
            SetCheckBoxValue(panel, "chkDeleteParagraphsWithText", settings.DeleteParagraphsWithText);
            SetTextBoxValue(panel, "txtParagraphKeywords", string.Join("\r\n", settings.ParagraphKeywords));
            SetCheckBoxValue(panel, "chkDeleteTextBoxesWithText", settings.DeleteTextBoxesWithText);
            SetTextBoxValue(panel, "txtTextBoxKeywords", string.Join("\r\n", settings.TextBoxKeywords));
            SetCheckBoxValue(panel, "chkDeleteTablesWithText", settings.DeleteTablesWithText);
            SetTextBoxValue(panel, "txtTableKeywords", string.Join("\r\n", settings.TableKeywords));
        }

        /// <summary>
        /// 加载图片删除设置
        /// </summary>
        private void LoadImageDeletionSettings()
        {
            var settings = _currentSettings.ImageDeletion;
            var panel = _tabPanels["ImageDeletion"];

            // 总开关
            SetCheckBoxValue(panel, "chkImageDeletionMaster", settings.EnableImageDeletion);

            // 删除范围设置
            SetDeletionScope(panel, "ImageDeletion", settings.DeletionScope);

            SetCheckBoxValue(panel, "chkDeleteAllImages", settings.DeleteAllImages);
            SetCheckBoxValue(panel, "chkDeleteSpecificImages", settings.EnableSpecificImageDeletion);
            SetTextBoxValue(panel, "txtSpecificImageNames", string.Join(",", settings.SpecificImageNames));
            SetCheckBoxValue(panel, "chkDeleteLastSlideImages", settings.DeleteLastSlideImages);
            SetCheckBoxValue(panel, "chkDeleteBackgroundImages", settings.DeleteBackgroundImages);
            SetCheckBoxValue(panel, "chkDeleteImageRange", settings.EnableSlideRangeImageDeletion);
            SetNumericUpDownValue(panel, "numImageRangeStart", settings.ImageSlideRangeStart);
            SetNumericUpDownValue(panel, "numImageRangeEnd", settings.ImageSlideRangeEnd);
            SetCheckBoxValue(panel, "chkDeletePositionImages", settings.EnablePositionImageDeletion);

            // 加载位置区域列表
            RefreshRegionList();

            // 为了向后兼容，如果区域列表为空但有旧的单个区域设置，则创建一个默认区域
            if (settings.PositionRegions.Count == 0 &&
                (settings.PositionXPercent != 0 || settings.PositionYPercent != 0 ||
                 settings.PositionWidthPercent != 20 || settings.PositionHeightPercent != 20))
            {
                var defaultRegion = new Models.PositionRegion("默认区域",
                    settings.PositionXPercent, settings.PositionYPercent,
                    settings.PositionWidthPercent, settings.PositionHeightPercent);
                settings.PositionRegions.Add(defaultRegion);
                RefreshRegionList();
            }

            // 加载当前编辑区域的值（保持向后兼容）
            SetNumericUpDownValue(panel, "numPositionXPercent", (decimal)settings.PositionXPercent);
            SetNumericUpDownValue(panel, "numPositionYPercent", (decimal)settings.PositionYPercent);
            SetNumericUpDownValue(panel, "numPositionWidthPercent", (decimal)settings.PositionWidthPercent);
            SetNumericUpDownValue(panel, "numPositionHeightPercent", (decimal)settings.PositionHeightPercent);
        }

        /// <summary>
        /// 加载表格删除设置
        /// </summary>
        private void LoadTableDeletionSettings()
        {
            var settings = _currentSettings.TableDeletion;
            var panel = _tabPanels["TableDeletion"];

            // 总开关
            SetCheckBoxValue(panel, "chkTableDeletionMaster", settings.EnableTableDeletion);

            // 删除范围设置
            SetDeletionScope(panel, "TableDeletion", settings.DeletionScope);

            SetCheckBoxValue(panel, "chkDeleteAllTables", settings.DeleteAllTables);
            SetCheckBoxValue(panel, "chkDeleteLastSlideTables", settings.DeleteLastSlideTables);
            SetCheckBoxValue(panel, "chkDeleteTableRange", settings.EnableSlideRangeTableDeletion);
            SetNumericUpDownValue(panel, "numTableRangeStart", settings.TableSlideRangeStart);
            SetNumericUpDownValue(panel, "numTableRangeEnd", settings.TableSlideRangeEnd);
        }

        /// <summary>
        /// 加载图表删除设置
        /// </summary>
        private void LoadChartDeletionSettings()
        {
            var settings = _currentSettings.ChartDeletion;
            var panel = _tabPanels["ChartDeletion"];

            // 总开关
            SetCheckBoxValue(panel, "chkChartDeletionMaster", settings.EnableChartDeletion);

            // 删除范围设置
            SetDeletionScope(panel, "ChartDeletion", settings.DeletionScope);

            SetCheckBoxValue(panel, "chkDeleteAllCharts", settings.DeleteAllCharts);
            SetCheckBoxValue(panel, "chkDeleteLastSlideCharts", settings.DeleteLastSlideCharts);
            SetCheckBoxValue(panel, "chkDeleteChartRange", settings.EnableSlideRangeChartDeletion);
            SetNumericUpDownValue(panel, "numChartRangeStart", settings.ChartSlideRangeStart);
            SetNumericUpDownValue(panel, "numChartRangeEnd", settings.ChartSlideRangeEnd);
            SetCheckBoxValue(panel, "chkDeleteChartsWithText", settings.DeleteChartsWithText);
            UpdateChartKeywordsCountLabel(settings.ChartKeywords.Count);
        }

        /// <summary>
        /// 加载音频视频删除设置
        /// </summary>
        private void LoadMediaDeletionSettings()
        {
            var settings = _currentSettings.MediaDeletion;
            var panel = _tabPanels["MediaDeletion"];

            // 总开关
            SetCheckBoxValue(panel, "chkMediaDeletionMaster", settings.EnableMediaDeletion);

            // 删除范围设置
            SetDeletionScope(panel, "MediaDeletion", settings.DeletionScope);

            SetCheckBoxValue(panel, "chkDeleteAllAudio", settings.DeleteAllAudio);
            SetCheckBoxValue(panel, "chkDeleteLastSlideAudio", settings.DeleteLastSlideAudio);
            SetCheckBoxValue(panel, "chkDeleteAudioRange", settings.EnableSlideRangeAudioDeletion);
            SetNumericUpDownValue(panel, "numAudioRangeStart", settings.AudioSlideRangeStart);
            SetNumericUpDownValue(panel, "numAudioRangeEnd", settings.AudioSlideRangeEnd);
            SetCheckBoxValue(panel, "chkDeleteAllVideo", settings.DeleteAllVideo);
            SetCheckBoxValue(panel, "chkDeleteLastSlideVideo", settings.DeleteLastSlideVideo);
            SetCheckBoxValue(panel, "chkDeleteVideoRange", settings.EnableSlideRangeVideoDeletion);
            SetNumericUpDownValue(panel, "numVideoRangeStart", settings.VideoSlideRangeStart);
            SetNumericUpDownValue(panel, "numVideoRangeEnd", settings.VideoSlideRangeEnd);
        }

        /// <summary>
        /// 加载联系方式删除设置
        /// </summary>
        private void LoadContactDeletionSettings()
        {
            var settings = _currentSettings.ContactDeletion;
            var panel = _tabPanels["ContactDeletion"];

            // 总开关
            SetCheckBoxValue(panel, "chkContactDeletionMaster", settings.EnableContactDeletion);

            // 删除范围设置
            SetDeletionScope(panel, "ContactDeletion", settings.DeletionScope);

            SetCheckBoxValue(panel, "chkDeletePhoneNumbers", settings.DeletePhoneNumbers);
            SetCheckBoxValue(panel, "chkDeleteLandlineNumbers", settings.DeleteLandlineNumbers);
            SetCheckBoxValue(panel, "chkDeleteEmailAddresses", settings.DeleteEmailAddresses);
            SetCheckBoxValue(panel, "chkDeleteWebsites", settings.DeleteWebsites);
            SetCheckBoxValue(panel, "chkDeleteHyperlinks", settings.DeleteHyperlinks);
        }

        /// <summary>
        /// 加载动画删除设置
        /// </summary>
        private void LoadAnimationDeletionSettings()
        {
            var settings = _currentSettings.AnimationDeletion;
            var panel = _tabPanels["AnimationDeletion"];

            // 总开关
            SetCheckBoxValue(panel, "chkAnimationDeletionMaster", settings.EnableAnimationDeletion);

            // 删除范围设置
            SetDeletionScope(panel, "AnimationDeletion", settings.DeletionScope);

            SetCheckBoxValue(panel, "chkDeleteAllAnimations", settings.DeleteAllAnimations);
            SetCheckBoxValue(panel, "chkDeleteLastSlideAnimations", settings.DeleteLastSlideAnimations);
            SetCheckBoxValue(panel, "chkDeleteAnimationRange", settings.EnableSlideRangeAnimationDeletion);
            SetNumericUpDownValue(panel, "numAnimationRangeStart", settings.AnimationSlideRangeStart);
            SetNumericUpDownValue(panel, "numAnimationRangeEnd", settings.AnimationSlideRangeEnd);
            SetCheckBoxValue(panel, "chkDeleteAllTransitions", settings.DeleteAllTransitions);
            SetCheckBoxValue(panel, "chkDeleteLastSlideTransitions", settings.DeleteLastSlideTransitions);
            SetCheckBoxValue(panel, "chkDeleteTransitionRange", settings.EnableSlideRangeTransitionDeletion);
            SetNumericUpDownValue(panel, "numTransitionRangeStart", settings.TransitionSlideRangeStart);
            SetNumericUpDownValue(panel, "numTransitionRangeEnd", settings.TransitionSlideRangeEnd);
        }

        /// <summary>
        /// 加载备注删除设置
        /// </summary>
        private void LoadNotesDeletionSettings()
        {
            var settings = _currentSettings.NotesDeletion;
            var panel = _tabPanels["NotesDeletion"];

            // 总开关
            SetCheckBoxValue(panel, "chkNotesDeletionMaster", settings.EnableNotesDeletion);

            // 删除范围设置
            SetDeletionScope(panel, "NotesDeletion", settings.DeletionScope);

            SetCheckBoxValue(panel, "chkDeleteSlideNotes", settings.DeleteSlideNotes);
            SetCheckBoxValue(panel, "chkClearNotesContent", settings.ClearNotesContent);
        }

        /// <summary>
        /// 加载格式删除设置
        /// </summary>
        private void LoadFormatDeletionSettings()
        {
            var settings = _currentSettings.FormatDeletion;
            var panel = _tabPanels["FormatDeletion"];

            // 总开关
            SetCheckBoxValue(panel, "chkFormatDeletionMaster", settings.EnableFormatDeletion);

            // 删除范围设置
            SetDeletionScope(panel, "FormatDeletion", settings.DeletionScope);

            // 文本格式删除选项
            SetCheckBoxValue(panel, "chkDeleteFontFormatting", settings.DeleteFontFormatting);
            SetCheckBoxValue(panel, "chkDeleteParagraphFormatting", settings.DeleteParagraphFormatting);
            SetCheckBoxValue(panel, "chkDeleteListFormatting", settings.DeleteListFormatting);

            // 对象格式删除选项
            SetCheckBoxValue(panel, "chkDeleteTableFormatting", settings.DeleteTableFormatting);
            SetCheckBoxValue(panel, "chkDeleteShapeFormatting", settings.DeleteShapeFormatting);
            SetCheckBoxValue(panel, "chkDeleteImageFormatting", settings.DeleteImageFormatting);
            SetCheckBoxValue(panel, "chkDeleteChartFormatting", settings.DeleteChartFormatting);

            // 背景格式删除选项
            SetCheckBoxValue(panel, "chkDeleteBackgroundFormatting", settings.DeleteBackgroundFormatting);
        }

        #region 事件处理方法

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void ContentDeletionForm_Load(object? sender, EventArgs e)
        {
            try
            {
                // 窗体加载完成后的初始化工作
                this.Text = "内容删除设置";

                // 调整标签页宽度以充分利用空间
                AdjustTabWidth();

                // 监听窗体大小变化事件
                this.Resize += ContentDeletionForm_Resize;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"窗体加载失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 窗体大小变化事件
        /// </summary>
        private void ContentDeletionForm_Resize(object? sender, EventArgs e)
        {
            try
            {
                // 窗体大小变化时重新调整标签页宽度
                AdjustTabWidth();
            }
            catch (Exception)
            {
                // 忽略调整过程中的错误，避免影响用户体验
            }
        }

        /// <summary>
        /// 调整标签页宽度以充分利用空间
        /// </summary>
        private void AdjustTabWidth()
        {
            try
            {
                if (tabControlMain == null) return;

                // 获取TabControl的可用宽度
                var availableWidth = tabControlMain.ClientSize.Width;

                // 考虑TabControl的内部边距（通常左右各有2-3px的边距）
                var usableWidth = availableWidth - 10;

                // 总共11个标签页，分为两行：第一行6个，第二行5个
                // 为了保持一致性，使用第一行的宽度（6个标签）作为标准
                var tabWidth = usableWidth / 6;

                // 确保宽度不小于最小值（避免标签太窄）
                if (tabWidth < 100)
                    tabWidth = 100;

                // 确保宽度不大于最大值（避免标签太宽）
                if (tabWidth > 200)
                    tabWidth = 200;

                // 设置新的标签大小
                tabControlMain.ItemSize = new Size(tabWidth, 40);
            }
            catch (Exception)
            {
                // 忽略调整过程中的错误
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void ContentDeletionForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            try
            {
                // 只有点击确定按钮或直接关闭窗口时才保存设置
                // 点击取消按钮时不保存设置
                if (this.DialogResult == DialogResult.OK || this.DialogResult == DialogResult.None)
                {
                    // 验证输入
                    if (!ValidateInputs())
                    {
                        e.Cancel = true; // 取消关闭操作
                        return;
                    }

                    SaveUIToSettings();
                    SaveSettingsToConfig();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                e.Cancel = true; // 取消关闭操作
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (!ValidateInputs())
                {
                    return;
                }

                SaveUIToSettings();
                SaveSettingsToConfig();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            // 设置取消结果，不保存配置文件
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }



        /// <summary>
        /// 文件名非法词按钮点击事件
        /// </summary>
        private void BtnFileNameIllegalWords_Click(object? sender, EventArgs e)
        {
            try
            {
                using var form = new IllegalWordsForm(
                    "文件名非法词设置",
                    "请输入文件名非法词，每行一个。如果文件名包含任何一个非法词，该文件将被删除。",
                    _currentSettings.DocumentDeletion.FileNameIllegalWords);

                if (form.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.DocumentDeletion.FileNameIllegalWords = form.GetIllegalWords();
                    UpdateIllegalWordsCountLabel("lblFileNameIllegalCount", _currentSettings.DocumentDeletion.FileNameIllegalWords.Count);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置文件名非法词失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 内容非法词按钮点击事件
        /// </summary>
        private void BtnContentIllegalWords_Click(object? sender, EventArgs e)
        {
            try
            {
                using var form = new IllegalWordsForm(
                    "内容非法词设置",
                    "请输入内容非法词，每行一个。如果文档内容包含任何一个非法词，该文件将被删除。",
                    _currentSettings.DocumentDeletion.ContentIllegalWords);

                if (form.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.DocumentDeletion.ContentIllegalWords = form.GetIllegalWords();
                    UpdateIllegalWordsCountLabel("lblContentIllegalCount", _currentSettings.DocumentDeletion.ContentIllegalWords.Count);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置内容非法词失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新非法词数量标签
        /// </summary>
        private void UpdateIllegalWordsCountLabel(string labelName, int count)
        {
            try
            {
                var label = FindControlByName(_tabPanels["DocumentDeletion"], labelName) as Label;
                if (label != null)
                {
                    label.Text = $"{count}个";
                    label.ForeColor = count > 0 ? Color.Blue : Color.Gray;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新非法词数量标签失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 验证用户输入 - 确保所有子功能的设置都正确且能独立执行
        /// </summary>
        /// <returns>验证是否通过</returns>
        private bool ValidateInputs()
        {
            try
            {
                // 验证文档删除设置
                if (!ValidateDocumentDeletionSettings())
                    return false;

                // 验证内容删除设置
                if (!ValidateContentRemovalSettings())
                    return false;

                // 验证文本删除设置
                if (!ValidateTextDeletionSettings())
                    return false;

                // 验证图片删除设置
                if (!ValidateImageDeletionSettings())
                    return false;

                // 验证表格删除设置
                if (!ValidateTableDeletionSettings())
                    return false;

                // 验证图表删除设置
                if (!ValidateChartDeletionSettings())
                    return false;

                // 验证媒体删除设置
                if (!ValidateMediaDeletionSettings())
                    return false;

                // 验证联系方式删除设置
                if (!ValidateContactDeletionSettings())
                    return false;

                // 验证动画删除设置
                if (!ValidateAnimationDeletionSettings())
                    return false;

                // 验证备注删除设置
                if (!ValidateNotesDeletionSettings())
                    return false;

                // 验证格式删除设置
                if (!ValidateFormatDeletionSettings())
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"验证输入时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 验证文档删除设置
        /// </summary>
        private bool ValidateDocumentDeletionSettings()
        {
            var panel = _tabPanels["DocumentDeletion"];

            // 验证文件名长度范围
            var minLength = (int)GetNumericUpDownValue(panel, "numFileNameMinLength");
            var maxLength = (int)GetNumericUpDownValue(panel, "numFileNameMaxLength");

            if (minLength > maxLength)
            {
                MessageBox.Show("文件名最小长度不能大于最大长度", "输入验证",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // 验证文件大小范围 - 需要转换为字节进行比较
            var minSize = GetNumericUpDownValue(panel, "numFileSizeMin");
            var maxSize = GetNumericUpDownValue(panel, "numFileSizeMax");
            var minUnit = GetComboBoxValue(panel, "cmbFileSizeMinUnit");
            var maxUnit = GetComboBoxValue(panel, "cmbFileSizeMaxUnit");

            // 转换为字节进行比较
            var minSizeInBytes = ConvertToBytes(minSize, minUnit);
            var maxSizeInBytes = ConvertToBytes(maxSize, maxUnit);

            if (minSizeInBytes > maxSizeInBytes)
            {
                MessageBox.Show("文件最小大小不能大于最大大小", "输入验证",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证图片删除设置
        /// </summary>
        private bool ValidateImageDeletionSettings()
        {
            var panel = _tabPanels["ImageDeletion"];

            // 验证位置百分比范围
            var positionXPercent = GetNumericUpDownValue(panel, "numPositionXPercent");
            var positionYPercent = GetNumericUpDownValue(panel, "numPositionYPercent");
            var positionWidthPercent = GetNumericUpDownValue(panel, "numPositionWidthPercent");
            var positionHeightPercent = GetNumericUpDownValue(panel, "numPositionHeightPercent");

            if (positionXPercent < 0 || positionXPercent > 100 ||
                positionYPercent < 0 || positionYPercent > 100 ||
                positionWidthPercent <= 0 || positionWidthPercent > 100 ||
                positionHeightPercent <= 0 || positionHeightPercent > 100)
            {
                MessageBox.Show("位置百分比必须在0-100范围内，宽度和高度必须大于0", "输入验证",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // 验证区域是否超出边界
            if (positionXPercent + positionWidthPercent > 100 ||
                positionYPercent + positionHeightPercent > 100)
            {
                MessageBox.Show("删除区域超出了幻灯片边界（左边距+宽度 ≤ 100%，上边距+高度 ≤ 100%）", "输入验证",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证表格删除设置
        /// </summary>
        private bool ValidateTableDeletionSettings()
        {
            var panel = _tabPanels["TableDeletion"];

            // 验证页面范围
            var rangeStart = (int)GetNumericUpDownValue(panel, "numTableRangeStart");
            var rangeEnd = (int)GetNumericUpDownValue(panel, "numTableRangeEnd");

            if (rangeStart > rangeEnd)
            {
                MessageBox.Show("表格删除起始页不能大于结束页", "输入验证",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (rangeStart < 1)
            {
                MessageBox.Show("页面编号必须从1开始", "输入验证",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证图表删除设置
        /// </summary>
        private bool ValidateChartDeletionSettings()
        {
            var panel = _tabPanels["ChartDeletion"];

            // 验证页面范围
            var rangeStart = (int)GetNumericUpDownValue(panel, "numChartRangeStart");
            var rangeEnd = (int)GetNumericUpDownValue(panel, "numChartRangeEnd");

            if (rangeStart > rangeEnd)
            {
                MessageBox.Show("图表删除起始页不能大于结束页", "输入验证",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (rangeStart < 1)
            {
                MessageBox.Show("页面编号必须从1开始", "输入验证",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证媒体删除设置
        /// </summary>
        private bool ValidateMediaDeletionSettings()
        {
            var panel = _tabPanels["MediaDeletion"];

            // 验证音频页面范围
            var audioRangeStart = (int)GetNumericUpDownValue(panel, "numAudioRangeStart");
            var audioRangeEnd = (int)GetNumericUpDownValue(panel, "numAudioRangeEnd");

            if (audioRangeStart > audioRangeEnd)
            {
                MessageBox.Show("音频删除起始页不能大于结束页", "输入验证",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // 验证视频页面范围
            var videoRangeStart = (int)GetNumericUpDownValue(panel, "numVideoRangeStart");
            var videoRangeEnd = (int)GetNumericUpDownValue(panel, "numVideoRangeEnd");

            if (videoRangeStart > videoRangeEnd)
            {
                MessageBox.Show("视频删除起始页不能大于结束页", "输入验证",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证内容删除设置
        /// </summary>
        private bool ValidateContentRemovalSettings()
        {
            var panel = _tabPanels["ContentRemoval"];

            // 验证幻灯片范围删除设置
            var chkSlideRange = GetCheckBoxValue(panel, "chkDeleteSlideRange");
            if (chkSlideRange)
            {
                var rangeStart = (int)GetNumericUpDownValue(panel, "numSlideRangeStart");
                var rangeEnd = (int)GetNumericUpDownValue(panel, "numSlideRangeEnd");

                if (rangeStart > rangeEnd)
                {
                    MessageBox.Show("幻灯片删除起始页不能大于结束页", "输入验证",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                if (rangeStart < 1)
                {
                    MessageBox.Show("页面编号必须从1开始", "输入验证",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 验证文本删除设置
        /// </summary>
        private bool ValidateTextDeletionSettings()
        {
            var panel = _tabPanels["TextDeletion"];

            // 验证文本范围删除设置
            var chkTextRange = GetCheckBoxValue(panel, "chkDeleteTextRange");
            if (chkTextRange)
            {
                var rangeStart = (int)GetNumericUpDownValue(panel, "numTextRangeStart");
                var rangeEnd = (int)GetNumericUpDownValue(panel, "numTextRangeEnd");

                if (rangeStart > rangeEnd)
                {
                    MessageBox.Show("文本删除起始页不能大于结束页", "输入验证",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                if (rangeStart < 1)
                {
                    MessageBox.Show("页面编号必须从1开始", "输入验证",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 验证联系方式删除设置
        /// </summary>
        private bool ValidateContactDeletionSettings()
        {
            // 联系方式删除设置目前没有需要验证的数值范围
            return true;
        }

        /// <summary>
        /// 验证动画删除设置
        /// </summary>
        private bool ValidateAnimationDeletionSettings()
        {
            var panel = _tabPanels["AnimationDeletion"];

            // 验证动画页面范围
            var animationRangeStart = (int)GetNumericUpDownValue(panel, "numAnimationRangeStart");
            var animationRangeEnd = (int)GetNumericUpDownValue(panel, "numAnimationRangeEnd");

            if (animationRangeStart > animationRangeEnd)
            {
                MessageBox.Show("动画删除起始页不能大于结束页", "输入验证",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // 验证切换效果页面范围
            var transitionRangeStart = (int)GetNumericUpDownValue(panel, "numTransitionRangeStart");
            var transitionRangeEnd = (int)GetNumericUpDownValue(panel, "numTransitionRangeEnd");

            if (transitionRangeStart > transitionRangeEnd)
            {
                MessageBox.Show("切换效果删除起始页不能大于结束页", "输入验证",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证备注删除设置
        /// </summary>
        private bool ValidateNotesDeletionSettings()
        {
            // 备注删除设置目前没有需要验证的数值范围
            return true;
        }

        /// <summary>
        /// 验证格式删除设置
        /// </summary>
        private bool ValidateFormatDeletionSettings()
        {
            // 格式删除设置目前没有需要验证的数值范围
            return true;
        }

        /// <summary>
        /// 窗体按键预处理，确保多行文本框按键不被拦截
        /// </summary>
        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            // 检查当前焦点控件是否为多行文本框
            if (this.ActiveControl is TextBox textBox && textBox.Multiline)
            {
                // 对于多行文本框，让其优先处理按键
                switch (keyData)
                {
                    case Keys.Enter:
                    case Keys.Space:
                    case Keys.Tab:
                        // 这些按键让文本框自己处理，不传递给窗体
                        return false;
                }
            }

            // 其他情况使用默认处理
            return base.ProcessCmdKey(ref msg, keyData);
        }

        #region 位置区域管理

        // 当前编辑的区域索引，-1表示新增模式
        private int _currentEditingRegionIndex = -1;

        /// <summary>
        /// 设置位置区域管理事件
        /// </summary>
        private void SetupPositionRegionEvents()
        {
            try
            {
                var panel = _tabPanels["ImageDeletion"];

                // 区域列表选择事件
                var lstRegions = FindControlByName(panel, "lstPositionRegions") as ListView;
                if (lstRegions != null)
                {
                    lstRegions.SelectedIndexChanged += LstPositionRegions_SelectedIndexChanged;
                }

                // 按钮事件
                var btnAdd = FindControlByName(panel, "btnAddPositionRegion") as Button;
                if (btnAdd != null)
                {
                    btnAdd.Click += BtnAddPositionRegion_Click;
                }

                var btnEdit = FindControlByName(panel, "btnEditPositionRegion") as Button;
                if (btnEdit != null)
                {
                    btnEdit.Click += BtnEditPositionRegion_Click;
                }

                var btnDelete = FindControlByName(panel, "btnDeletePositionRegion") as Button;
                if (btnDelete != null)
                {
                    btnDelete.Click += BtnDeletePositionRegion_Click;
                }

                var btnSave = FindControlByName(panel, "btnSavePositionRegion") as Button;
                if (btnSave != null)
                {
                    btnSave.Click += BtnSavePositionRegion_Click;
                }

                var btnCancel = FindControlByName(panel, "btnCancelEditRegion") as Button;
                if (btnCancel != null)
                {
                    btnCancel.Click += BtnCancelEditRegion_Click;
                }

                var btnHelper = FindControlByName(panel, "btnPositionRegionHelper") as Button;
                if (btnHelper != null)
                {
                    btnHelper.Click += BtnPositionRegionHelper_Click;
                }

                // 初始化编辑区域状态
                SetEditAreaEnabled(false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置位置区域事件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        /// <summary>
        /// 刷新区域列表显示
        /// </summary>
        private void RefreshRegionList()
        {
            try
            {
                var panel = _tabPanels["ImageDeletion"];
                var lstRegions = FindControlByName(panel, "lstPositionRegions") as ListView;
                if (lstRegions == null) return;

                lstRegions.Items.Clear();

                // 使用序号而不是勾勾，并设置文字居中对齐
                int index = 1;
                foreach (var region in _currentSettings.ImageDeletion.PositionRegions)
                {
                    var item = new ListViewItem(index.ToString());
                    item.SubItems.Add(region.Name);
                    item.SubItems.Add(region.XPercent.ToString("F1"));
                    item.SubItems.Add(region.YPercent.ToString("F1"));
                    item.SubItems.Add(region.WidthPercent.ToString("F1"));
                    item.SubItems.Add(region.HeightPercent.ToString("F1"));
                    item.Tag = region;

                    // 设置所有子项文字居中对齐
                    foreach (ListViewItem.ListViewSubItem subItem in item.SubItems)
                    {
                        subItem.Font = new Font("Microsoft YaHei UI", 10F);
                    }

                    lstRegions.Items.Add(item);
                    index++;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"刷新区域列表失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置编辑区域启用状态
        /// </summary>
        /// <param name="enabled">是否启用</param>
        private void SetEditAreaEnabled(bool enabled)
        {
            try
            {
                var panel = _tabPanels["ImageDeletion"];

                var txtName = FindControlByName(panel, "txtRegionName") as TextBox;
                var numX = FindControlByName(panel, "numPositionXPercent") as NumericUpDown;
                var numY = FindControlByName(panel, "numPositionYPercent") as NumericUpDown;
                var numWidth = FindControlByName(panel, "numPositionWidthPercent") as NumericUpDown;
                var numHeight = FindControlByName(panel, "numPositionHeightPercent") as NumericUpDown;
                var btnSave = FindControlByName(panel, "btnSavePositionRegion") as Button;
                var btnCancel = FindControlByName(panel, "btnCancelEditRegion") as Button;

                if (txtName != null) txtName.Enabled = enabled;
                if (numX != null) numX.Enabled = enabled;
                if (numY != null) numY.Enabled = enabled;
                if (numWidth != null) numWidth.Enabled = enabled;
                if (numHeight != null) numHeight.Enabled = enabled;
                if (btnSave != null) btnSave.Enabled = enabled;
                if (btnCancel != null) btnCancel.Enabled = enabled;

                if (!enabled)
                {
                    // 清空编辑区域
                    if (txtName != null) txtName.Text = "";
                    if (numX != null) numX.Value = 0;
                    if (numY != null) numY.Value = 0;
                    if (numWidth != null) numWidth.Value = 20;
                    if (numHeight != null) numHeight.Value = 20;
                    _currentEditingRegionIndex = -1;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置编辑区域状态失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 区域列表选择变化事件
        /// </summary>
        private void LstPositionRegions_SelectedIndexChanged(object? sender, EventArgs e)
        {
            try
            {
                var lstRegions = sender as ListView;
                if (lstRegions?.SelectedItems.Count > 0)
                {
                    var selectedItem = lstRegions.SelectedItems[0];
                    var region = selectedItem.Tag as Models.PositionRegion;
                    if (region != null)
                    {
                        // 在编辑区域显示选中的区域信息
                        var panel = _tabPanels["ImageDeletion"];
                        var txtName = FindControlByName(panel, "txtRegionName") as TextBox;
                        var numX = FindControlByName(panel, "numPositionXPercent") as NumericUpDown;
                        var numY = FindControlByName(panel, "numPositionYPercent") as NumericUpDown;
                        var numWidth = FindControlByName(panel, "numPositionWidthPercent") as NumericUpDown;
                        var numHeight = FindControlByName(panel, "numPositionHeightPercent") as NumericUpDown;

                        if (txtName != null) txtName.Text = region.Name;
                        if (numX != null) numX.Value = (decimal)region.XPercent;
                        if (numY != null) numY.Value = (decimal)region.YPercent;
                        if (numWidth != null) numWidth.Value = (decimal)region.WidthPercent;
                        if (numHeight != null) numHeight.Value = (decimal)region.HeightPercent;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择区域失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 新增区域按钮点击事件
        /// </summary>
        private void BtnAddPositionRegion_Click(object? sender, EventArgs e)
        {
            try
            {
                _currentEditingRegionIndex = -1;
                SetEditAreaEnabled(true);

                // 设置默认值
                var panel = _tabPanels["ImageDeletion"];
                var txtName = FindControlByName(panel, "txtRegionName") as TextBox;
                if (txtName != null) txtName.Text = $"区域{_currentSettings.ImageDeletion.PositionRegions.Count + 1}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新增区域失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑区域按钮点击事件
        /// </summary>
        private void BtnEditPositionRegion_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["ImageDeletion"];
                var lstRegions = FindControlByName(panel, "lstPositionRegions") as ListView;

                if (lstRegions?.SelectedItems.Count > 0)
                {
                    _currentEditingRegionIndex = lstRegions.SelectedItems[0].Index;
                    SetEditAreaEnabled(true);
                }
                else
                {
                    MessageBox.Show("请先选择要编辑的区域", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑区域失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除区域按钮点击事件
        /// </summary>
        private void BtnDeletePositionRegion_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["ImageDeletion"];
                var lstRegions = FindControlByName(panel, "lstPositionRegions") as ListView;

                if (lstRegions?.SelectedItems.Count > 0)
                {
                    var selectedIndex = lstRegions.SelectedItems[0].Index;
                    // 删除多余的确认提示框，直接删除选中的区域
                    _currentSettings.ImageDeletion.PositionRegions.RemoveAt(selectedIndex);
                    RefreshRegionList();
                    SetEditAreaEnabled(false);
                }
                else
                {
                    MessageBox.Show("请先选择要删除的区域", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除区域失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存区域按钮点击事件
        /// </summary>
        private void BtnSavePositionRegion_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["ImageDeletion"];
                var txtName = FindControlByName(panel, "txtRegionName") as TextBox;
                var numX = FindControlByName(panel, "numPositionXPercent") as NumericUpDown;
                var numY = FindControlByName(panel, "numPositionYPercent") as NumericUpDown;
                var numWidth = FindControlByName(panel, "numPositionWidthPercent") as NumericUpDown;
                var numHeight = FindControlByName(panel, "numPositionHeightPercent") as NumericUpDown;

                // 验证输入
                if (string.IsNullOrWhiteSpace(txtName?.Text))
                {
                    MessageBox.Show("请输入区域名称", "输入验证", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName?.Focus();
                    return;
                }

                // 检查名称是否重复（编辑模式下排除自己）
                var existingRegion = _currentSettings.ImageDeletion.PositionRegions
                    .Where((r, index) => index != _currentEditingRegionIndex && r.Name == txtName.Text)
                    .FirstOrDefault();

                if (existingRegion != null)
                {
                    MessageBox.Show("区域名称已存在，请使用其他名称", "输入验证", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName?.Focus();
                    return;
                }

                // 创建或更新区域
                var region = new Models.PositionRegion
                {
                    Name = txtName.Text.Trim(),
                    XPercent = (float)(numX?.Value ?? 0),
                    YPercent = (float)(numY?.Value ?? 0),
                    WidthPercent = (float)(numWidth?.Value ?? 20),
                    HeightPercent = (float)(numHeight?.Value ?? 20),
                    IsEnabled = true
                };

                if (_currentEditingRegionIndex >= 0)
                {
                    // 编辑模式：更新现有区域
                    _currentSettings.ImageDeletion.PositionRegions[_currentEditingRegionIndex] = region;
                }
                else
                {
                    // 新增模式：添加新区域
                    _currentSettings.ImageDeletion.PositionRegions.Add(region);
                }

                RefreshRegionList();
                SetEditAreaEnabled(false);
                MessageBox.Show("区域保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存区域失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消编辑按钮点击事件
        /// </summary>
        private void BtnCancelEditRegion_Click(object? sender, EventArgs e)
        {
            SetEditAreaEnabled(false);
        }

        /// <summary>
        /// 区域助手按钮点击事件
        /// </summary>
        private void BtnPositionRegionHelper_Click(object? sender, EventArgs e)
        {
            try
            {
                var helperForm = new PositionHelperForm();
                if (helperForm.ShowDialog() == DialogResult.OK)
                {
                    var panel = _tabPanels["ImageDeletion"];
                    var numX = FindControlByName(panel, "numPositionXPercent") as NumericUpDown;
                    var numY = FindControlByName(panel, "numPositionYPercent") as NumericUpDown;
                    var numWidth = FindControlByName(panel, "numPositionWidthPercent") as NumericUpDown;
                    var numHeight = FindControlByName(panel, "numPositionHeightPercent") as NumericUpDown;

                    // 添加边界检查，确保值在有效范围内
                    if (numX != null) numX.Value = Math.Max(0, Math.Min(100, (decimal)helperForm.SelectedXPercent));
                    if (numY != null) numY.Value = Math.Max(0, Math.Min(100, (decimal)helperForm.SelectedYPercent));
                    if (numWidth != null) numWidth.Value = Math.Max(1, Math.Min(100, (decimal)helperForm.SelectedWidthPercent));
                    if (numHeight != null) numHeight.Value = Math.Max(1, Math.Min(100, (decimal)helperForm.SelectedHeightPercent));
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"使用区域助手失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #endregion

        #region 图表关键词管理功能

        /// <summary>
        /// 设置图表关键词按钮事件
        /// </summary>
        private void SetupChartKeywordsButtonEvents()
        {
            try
            {
                var panel = _tabPanels["ChartDeletion"];
                var btnEditChartKeywords = FindControlByName(panel, "btnEditChartKeywords") as Button;
                if (btnEditChartKeywords != null)
                {
                    btnEditChartKeywords.Click += BtnEditChartKeywords_Click;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置图表关键词按钮事件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑图表关键词按钮点击事件
        /// </summary>
        private void BtnEditChartKeywords_Click(object? sender, EventArgs e)
        {
            try
            {
                var settings = _currentSettings.ChartDeletion;
                var keywords = string.Join("\r\n", settings.ChartKeywords);

                using (var form = new Forms.KeywordEditForm("图表关键词编辑", keywords))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // 更新关键词列表
                        var newKeywords = form.Keywords
                            .Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries)
                            .Select(k => k.Trim())
                            .Where(k => !string.IsNullOrEmpty(k))
                            .ToList();

                        settings.ChartKeywords = newKeywords;

                        // 更新界面显示
                        UpdateChartKeywordsCountLabel(newKeywords.Count);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑图表关键词失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新图表关键词数量标签
        /// </summary>
        private void UpdateChartKeywordsCountLabel(int count)
        {
            try
            {
                var panel = _tabPanels["ChartDeletion"];
                var lblCount = FindControlByName(panel, "lblChartKeywordsCount") as Label;
                if (lblCount != null)
                {
                    lblCount.Text = $"({count}个关键词)";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新图表关键词数量标签失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 图片管理功能

        /// <summary>
        /// 设置图片管理按钮事件
        /// </summary>
        private void SetupImageManagementButtonEvents()
        {
            try
            {
                var panel = _tabPanels["ContentRemoval"];

                var btnAddImage = FindControlByName(panel, "btnAddSpecificImage") as Button;
                if (btnAddImage != null)
                {
                    btnAddImage.Click += BtnAddSpecificImage_Click;
                }

                var btnRemoveImage = FindControlByName(panel, "btnRemoveSpecificImage") as Button;
                if (btnRemoveImage != null)
                {
                    btnRemoveImage.Click += BtnRemoveSpecificImage_Click;
                }

                var btnClearImages = FindControlByName(panel, "btnClearSpecificImages") as Button;
                if (btnClearImages != null)
                {
                    btnClearImages.Click += BtnClearSpecificImages_Click;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置图片管理按钮事件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 添加图片按钮点击事件
        /// </summary>
        private void BtnAddSpecificImage_Click(object? sender, EventArgs e)
        {
            try
            {
                using (var openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Title = "选择要添加的图片文件";
                    openFileDialog.Filter = "图片文件|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff;*.webp|所有文件|*.*";
                    openFileDialog.Multiselect = true; // 允许多选
                    openFileDialog.CheckFileExists = true;
                    openFileDialog.CheckPathExists = true;

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        var panel = _tabPanels["ContentRemoval"];
                        var lstImages = FindControlByName(panel, "lstSpecificImages") as ListView;
                        if (lstImages != null)
                        {
                            foreach (var filePath in openFileDialog.FileNames)
                            {
                                // 检查是否已经存在
                                bool exists = false;
                                foreach (ListViewItem item in lstImages.Items)
                                {
                                    if (item.SubItems[2].Text == filePath)
                                    {
                                        exists = true;
                                        break;
                                    }
                                }

                                if (!exists)
                                {
                                    var fileName = Path.GetFileName(filePath);
                                    var item = new ListViewItem((lstImages.Items.Count + 1).ToString());
                                    item.SubItems.Add(fileName);
                                    item.SubItems.Add(filePath);
                                    item.Tag = filePath; // 存储完整路径
                                    lstImages.Items.Add(item);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加图片文件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除图片按钮点击事件
        /// </summary>
        private void BtnRemoveSpecificImage_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["ContentRemoval"];
                var lstImages = FindControlByName(panel, "lstSpecificImages") as ListView;
                if (lstImages != null && lstImages.SelectedItems.Count > 0)
                {
                    var selectedItem = lstImages.SelectedItems[0];
                    lstImages.Items.Remove(selectedItem);

                    // 重新编号
                    RefreshImageListNumbers(lstImages);
                }
                else
                {
                    MessageBox.Show("请先选择要删除的图片", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除图片失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空图片列表按钮点击事件
        /// </summary>
        private void BtnClearSpecificImages_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["ContentRemoval"];
                var lstImages = FindControlByName(panel, "lstSpecificImages") as ListView;
                if (lstImages != null)
                {
                    if (lstImages.Items.Count > 0)
                    {
                        var result = MessageBox.Show("确定要清空所有图片吗？", "确认",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                        if (result == DialogResult.Yes)
                        {
                            lstImages.Items.Clear();
                        }
                    }
                    else
                    {
                        MessageBox.Show("图片列表已经是空的", "提示",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空图片列表失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 刷新图片列表的序号
        /// </summary>
        private static void RefreshImageListNumbers(ListView lstImages)
        {
            for (int i = 0; i < lstImages.Items.Count; i++)
            {
                lstImages.Items[i].Text = (i + 1).ToString();
            }
        }

        /// <summary>
        /// 加载指定图片列表
        /// </summary>
        private void LoadSpecificImagesList(Panel panel, List<string> imagePaths)
        {
            try
            {
                var lstImages = FindControlByName(panel, "lstSpecificImages") as ListView;
                if (lstImages != null)
                {
                    lstImages.Items.Clear();
                    for (int i = 0; i < imagePaths.Count; i++)
                    {
                        var filePath = imagePaths[i];
                        if (!string.IsNullOrWhiteSpace(filePath))
                        {
                            var fileName = Path.GetFileName(filePath);
                            var item = new ListViewItem((i + 1).ToString());
                            item.SubItems.Add(fileName);
                            item.SubItems.Add(filePath);
                            item.Tag = filePath;
                            lstImages.Items.Add(item);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载图片列表失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存指定图片列表
        /// </summary>
        private List<string> SaveSpecificImagesList(Panel panel)
        {
            var imagePaths = new List<string>();
            try
            {
                var lstImages = FindControlByName(panel, "lstSpecificImages") as ListView;
                if (lstImages != null)
                {
                    foreach (ListViewItem item in lstImages.Items)
                    {
                        var filePath = item.Tag?.ToString();
                        if (!string.IsNullOrWhiteSpace(filePath))
                        {
                            imagePaths.Add(filePath);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存图片列表失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            return imagePaths;
        }

        #endregion

        #region 删除范围选择控件

        /// <summary>
        /// 创建删除范围选择区域 - 用于选择删除操作的作用范围（普通幻灯片页、母版、版式页）
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="width">宽度</param>
        /// <param name="controlNamePrefix">控件名称前缀</param>
        /// <returns>删除范围选择分组框</returns>
        private GroupBox CreateDeletionScopeGroupBox(int x, int y, int width, string controlNamePrefix)
        {
            var grpDeletionScope = CreateGroupBox("删除范围", x, y, width, 90); // 增加高度以适应40像素高度的控件
            grpDeletionScope.Name = $"grp{controlNamePrefix}DeletionScope";

            // 普通幻灯片页复选框
            var chkNormalSlides = CreateCheckBox("普通幻灯片页", 30, 40, 180, 40);
            chkNormalSlides.Name = $"chk{controlNamePrefix}NormalSlides";
            chkNormalSlides.Checked = true; // 默认选中

            // 母版复选框
            var chkMasters = CreateCheckBox("母版", 230, 40, 100, 40);
            chkMasters.Name = $"chk{controlNamePrefix}Masters";

            // 母版版式页复选框
            var chkLayoutSlides = CreateCheckBox("母版版式页", 350, 40, 150, 40);
            chkLayoutSlides.Name = $"chk{controlNamePrefix}LayoutSlides";

            grpDeletionScope.Controls.AddRange(new Control[] {
                chkNormalSlides, chkMasters, chkLayoutSlides
            });

            return grpDeletionScope;
        }



        #endregion

    }
}
